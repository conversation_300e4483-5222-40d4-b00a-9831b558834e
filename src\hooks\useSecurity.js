import { useEffect } from 'react';
import secureStorage from '../utils/SecureStorage';
import securityMonitor from '../utils/SecurityMonitor';

/**
 * Hook personalizado para manejar la seguridad del dashboard
 * Centraliza la lógica de monitoreo de seguridad
 */
const useSecurity = (currentUser, expectedRole, onLogout) => {
  useEffect(() => {
    securityMonitor.start();

    // Log de acceso (se puede configurar para ser más silencioso en producción)
    if (process.env.NODE_ENV === 'development') {
      console.log(`[SECURITY] Dashboard accessed at: ${new Date().toISOString()}`);
      console.log('[SECURITY] User info:', currentUser);
    }

    // Verificar integridad de datos de usuario
    const userRole = secureStorage.getSecure('user_role') || sessionStorage.getItem('userRole');
    
    // Validar rol según el tipo esperado
    const isValidRole = Array.isArray(expectedRole) 
      ? expectedRole.includes(userRole)
      : userRole === expectedRole;

    if (!isValidRole) {
      console.error('[SECURITY] Invalid user role detected:', userRole);
      onLogout();
    }

    return () => {
      securityMonitor.stop();
    };
  }, [currentUser, expectedRole, onLogout]);
};

export default useSecurity;
