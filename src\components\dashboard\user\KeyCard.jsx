import { Trash2, <PERSON> } from 'lucide-react';
import PropTypes from 'prop-types';
import { Button } from '../../common';

/**
 * Componente para mostrar información de una llave en formato de tarjeta
 */
const KeyCard = ({ keyData, darkMode, onDelete, onViewDetail, disabled }) => {
  const getStatusInfo = (key) => {
    if (key.status === 'UPLOADED_TO_CTM' || key.uploadedToCtm === true) {
      return {
        text: 'uploaded_to_ctm',
        displayText: 'Activa',
        className: 'bg-gradient-to-r from-green-500 to-emerald-600 text-white shadow-lg',
        dotColor: 'bg-green-400'
      };
    }
    if (key.status === 'FAILED' || key.isSuccessful === false) {
      return {
        text: 'failed',
        displayText: 'Fallida',
        className: 'bg-gradient-to-r from-red-500 to-rose-600 text-white shadow-lg',
        dotColor: 'bg-red-400'
      };
    }
    return {
      text: 'pending',
      displayText: 'Pendiente',
      className: 'bg-gradient-to-r from-yellow-500 to-amber-600 text-white shadow-lg',
      dotColor: 'bg-yellow-400'
    };
  };

  const getTypeInfo = (key) => {
    const type = key.type || key.algorithm || 'unknown';
    return {
      text: type,
      className: 'bg-gradient-to-r from-blue-500 to-indigo-600 text-white shadow-md'
    };
  };

  const statusInfo = getStatusInfo(keyData);
  const typeInfo = getTypeInfo(keyData);

  return (
    <div
      className={`p-6 rounded-2xl border-2 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 ${
        darkMode
          ? 'bg-gradient-to-br from-gray-800 to-gray-700 border-gray-600 hover:border-gray-500'
          : 'bg-gradient-to-br from-white to-gray-50 border-gray-200 hover:border-gray-300'
      }`}
    >
      <div className="flex justify-between items-start">
        <div className="flex-1">
          {/* Header con nombre y etiquetas */}
          <div className="flex items-center gap-3 mb-4">
            <h3 className="text-xl font-light tracking-wide text-gray-900 dark:text-white">
              {keyData.name}
            </h3>

            {/* Etiqueta de estado moderna */}
            <div className={`flex items-center gap-2 px-3 py-1.5 rounded-xl text-xs font-medium ${statusInfo.className}`}>
              <div className={`w-2 h-2 rounded-full ${statusInfo.dotColor} animate-pulse`}></div>
              {statusInfo.text}
            </div>

            {/* Etiqueta de tipo moderna */}
            <div className={`px-3 py-1.5 rounded-xl text-xs font-medium ${typeInfo.className}`}>
              {typeInfo.text}
            </div>
          </div>

          {/* ID de la llave con diseño elegante */}
          <div className="mb-4">
            <div className={`p-4 rounded-xl border-2 ${
              darkMode
                ? 'bg-gray-900/50 border-gray-600'
                : 'bg-gray-50 border-gray-200'
            }`}>
              <p className="font-mono text-sm text-gray-700 dark:text-gray-300 break-all">
                {keyData.ctmKeyId || keyData.id}
              </p>
              <p className="text-xs font-light tracking-wide text-gray-500 dark:text-gray-400 mt-1">
                {keyData.ctmKeyId ? 'CTM Key ID' : 'ID Local'}
              </p>
            </div>
          </div>

          {/* Información en grid elegante */}
          <div className="grid grid-cols-2 gap-4">
            <div className={`p-3 rounded-xl ${
              darkMode ? 'bg-gray-900/30' : 'bg-white/70'
            }`}>
              <span className="text-xs font-light tracking-wide text-gray-600 dark:text-gray-400">Creada:</span>
              <p className="font-light tracking-wide text-gray-900 dark:text-white">
                {keyData.createdAt ? new Date(keyData.createdAt).toLocaleDateString('es-ES') : 'N/A'}
              </p>
            </div>
            <div className={`p-3 rounded-xl ${
              darkMode ? 'bg-gray-900/30' : 'bg-white/70'
            }`}>
              <span className="text-xs font-light tracking-wide text-gray-600 dark:text-gray-400">Algoritmo:</span>
              <p className="font-light tracking-wide text-gray-900 dark:text-white">
                {keyData.algorithm || keyData.type || 'N/A'}
              </p>
            </div>
          </div>
        </div>

        {/* Botones de acción modernos */}
        <div className="flex flex-col gap-3 ml-6">
          <button
            onClick={() => onViewDetail(keyData)}
            disabled={disabled}
            className={`p-3 rounded-xl transition-all duration-200 ${
              disabled
                ? 'bg-gray-100 text-gray-400 cursor-not-allowed dark:bg-gray-800 dark:text-gray-600'
                : 'bg-gradient-to-br from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700 text-white shadow-lg hover:shadow-xl transform hover:scale-105'
            }`}
            title="Ver detalle de la llave"
          >
            <Eye size={18} />
          </button>
          <button
            onClick={onDelete}
            disabled={disabled}
            className={`p-3 rounded-xl transition-all duration-200 ${
              disabled
                ? 'bg-gray-100 text-gray-400 cursor-not-allowed dark:bg-gray-800 dark:text-gray-600'
                : 'bg-gradient-to-br from-red-500 to-rose-600 hover:from-red-600 hover:to-rose-700 text-white shadow-lg hover:shadow-xl transform hover:scale-105'
            }`}
            title="Eliminar llave"
          >
            <Trash2 size={18} />
          </button>
        </div>
      </div>
    </div>
  );
};

KeyCard.propTypes = {
  keyData: PropTypes.object.isRequired,
  darkMode: PropTypes.bool.isRequired,
  onDelete: PropTypes.func.isRequired,
  onViewDetail: PropTypes.func.isRequired,
  disabled: PropTypes.bool
};

export default KeyCard;
