import { useState } from 'react';
import { Plus, Trash2 } from 'lucide-react';
import PropTypes from 'prop-types';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>rror<PERSON><PERSON><PERSON> } from '../../common';
import KeyCard from './KeyCard';
import KeyUploadModal from './KeyUploadModal';
import KeyDeleteModal from './KeyDeleteModal';
import { KeyDetailModal } from '../admin'; // Reutilizamos el modal del admin

/**
 * Componente para gestión de llaves en el UsuarioDashboard
 */
const KeyManagement = ({
  keys,
  isLoading,
  error,
  onClearError,
  onUploadKey,
  onDeleteKey,
  darkMode
}) => {
  const [showUploadModal, setShowUploadModal] = useState(false);
  const [showDetailModal, setShowDetailModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [selectedKey, setSelectedKey] = useState(null);
  const [keyToDelete, setKeyToDelete] = useState(null);

  const handleUploadKey = async (keyData) => {
    await onUploadKey(keyData);
    setShowUploadModal(false);
  };

  const handleDeleteKey = (keyId, keyName) => {
    setKeyToDelete({ id: keyId, name: keyName });
    setShowDeleteModal(true);
  };

  const handleConfirmDelete = async () => {
    if (keyToDelete) {
      await onDeleteKey(keyToDelete.id);
      setShowDeleteModal(false);
      setKeyToDelete(null);
    }
  };

  const handleCancelDelete = () => {
    setShowDeleteModal(false);
    setKeyToDelete(null);
  };

  const handleViewDetail = (key) => {
    setSelectedKey(key);
    setShowDetailModal(true);
  };

  const handleCloseDetailModal = () => {
    setShowDetailModal(false);
    setSelectedKey(null);
  };

  return (
    <>
      {/* Header con mensaje de bienvenida y botón */}
      <div className="mb-8">
        <div className="flex flex-col sm:flex-row sm:justify-between sm:items-start gap-4 mb-6">
          <div>
            <h1 className="text-3xl font-light tracking-wide leading-relaxed mb-4 text-gray-900 dark:text-white">
              Mis Llaves Cuánticas
            </h1>
            <p className="text-gray-600 dark:text-gray-300 font-light tracking-wide">
              Gestiona tus llaves cuánticas, visualiza detalles y sube nuevas llaves al sistema CTM.
            </p>
          </div>

          {/* Botón alineado a la derecha */}
          <div className="flex justify-end">
            <button
              onClick={() => setShowUploadModal(true)}
              className="group relative overflow-hidden px-4 py-2 rounded-xl font-light tracking-wide text-white bg-gradient-to-r from-blue-500 via-blue-600 to-blue-700 hover:from-blue-600 hover:via-blue-700 hover:to-blue-800 shadow-lg hover:shadow-xl hover:shadow-blue-500/30 transform hover:scale-105 transition-all duration-300 border border-blue-400/30"
            >
              <div className="absolute inset-0 bg-gradient-to-r from-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              <div className="relative flex items-center gap-2">
                <div className="p-1 rounded-lg bg-white/20 group-hover:bg-white/30 transition-all duration-300">
                  <Plus size={14} />
                </div>
                <span>Subir Nueva Llave</span>
              </div>
            </button>
          </div>
        </div>
      </div>

      <ErrorAlert error={error} onClose={onClearError} />

      {isLoading ? (
        <LoadingSpinner message="Cargando llaves..." />
      ) : (
        <div className="space-y-4">
          {keys.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              No tienes llaves registradas. ¡Crea tu primera llave!
            </div>
          ) : (
            keys.map((key) => (
              <KeyCard
                key={key.id}
                keyData={key}
                darkMode={darkMode}
                onDelete={() => handleDeleteKey(key.id, key.name)}
                onViewDetail={handleViewDetail}
                disabled={isLoading}
              />
            ))
          )}
        </div>
      )}

      <KeyUploadModal
        isOpen={showUploadModal}
        onClose={() => setShowUploadModal(false)}
        onUpload={handleUploadKey}
        darkMode={darkMode}
      />

      <KeyDetailModal
        isOpen={showDetailModal}
        onClose={handleCloseDetailModal}
        keyData={selectedKey}
        darkMode={darkMode}
      />

      <KeyDeleteModal
        isOpen={showDeleteModal}
        onClose={handleCancelDelete}
        onConfirm={handleConfirmDelete}
        keyName={keyToDelete?.name || ''}
        darkMode={darkMode}
      />
    </>
  );
};

KeyManagement.propTypes = {
  keys: PropTypes.array.isRequired,
  isLoading: PropTypes.bool.isRequired,
  error: PropTypes.string,
  onClearError: PropTypes.func.isRequired,
  onUploadKey: PropTypes.func.isRequired,
  onDeleteKey: PropTypes.func.isRequired,
  darkMode: PropTypes.bool.isRequired
};

export default KeyManagement;
