import { useRef, useEffect, useState } from 'react';
import gsap from 'gsap';
import PropTypes from 'prop-types';
import { Globe, ChevronDown } from 'lucide-react';
import { useLanguage } from '../../i18n';

/**
 * Componente MainContent reutilizable
 * Proporciona el contenedor principal con animaciones consistentes
 */
const MainContent = ({
  children,
  darkMode,
  className = ""
}) => {
  const mainContentRef = useRef(null);
  const { language, changeLanguage, t } = useLanguage();
  const [showDropdown, setShowDropdown] = useState(false);

  // Animaciones GSAP al montar
  useEffect(() => {
    gsap.fromTo(
      mainContentRef.current,
      { y: 100, opacity: 0 },
      { y: 0, opacity: 1, duration: 1, ease: "power2.out", delay: 0.3 }
    );
  }, []);

  const handleLanguageChange = (newLanguage) => {
    changeLanguage(newLanguage);
    setShowDropdown(false);
  };

  return (
    <main
      ref={mainContentRef}
      className={`flex-1 p-6 sm:p-10 overflow-y-auto relative transition-colors duration-500
        ${darkMode ? "bg-gray-700" : "bg-gray-50"} ${className}`}
    >
      {/* Botón de idioma ARRIBA de la card */}
      <div className="flex justify-end mb-4 relative z-50">
        <div className="relative">
          <button
            onClick={() => setShowDropdown(!showDropdown)}
            className={`flex items-center gap-2 px-3 py-2 rounded-lg border transition-all duration-200 text-sm ${
              darkMode
                ? 'bg-gray-600 border-gray-500 text-gray-200 hover:bg-gray-500'
                : 'bg-gray-100 border-gray-200 text-blue-600 hover:bg-gray-200'
            }`}
          >
            <Globe size={14} className="text-blue-500" />
            <span className="font-medium">{t('languages.' + language)}</span>
            <ChevronDown size={12} className={`transition-transform duration-200 ${showDropdown ? 'rotate-180' : ''}`} />
          </button>

          {/* Dropdown con z-index alto */}
          {showDropdown && (
            <div className="absolute top-full right-0 mt-1 w-28 bg-white rounded-lg shadow-xl border border-gray-200 overflow-hidden z-50">
              <button
                onClick={() => handleLanguageChange('en')}
                className={`w-full px-3 py-2 text-left text-sm transition-colors duration-200 flex items-center gap-2 ${
                  language === 'en'
                    ? 'bg-blue-500 text-white'
                    : 'text-gray-700 hover:bg-gray-50'
                }`}
              >
                <Globe size={12} />
                {t('languages.en')}
              </button>
              <button
                onClick={() => handleLanguageChange('es')}
                className={`w-full px-3 py-2 text-left text-sm transition-colors duration-200 flex items-center gap-2 ${
                  language === 'es'
                    ? 'bg-blue-500 text-white'
                    : 'text-gray-700 hover:bg-gray-50'
                }`}
              >
                <Globe size={12} />
                {t('languages.es')}
              </button>
            </div>
          )}
        </div>
      </div>

      {/* Contenedor principal */}
      <div
        className={`relative z-10 transition-all duration-500 rounded-xl p-6 sm:p-8 shadow-md border
          ${darkMode
            ? "bg-gray-800 border-gray-700 text-white"
            : "bg-white border-gray-200 text-gray-900"}`}
      >
        {children}
      </div>
    </main>
  );
};

MainContent.propTypes = {
  children: PropTypes.node.isRequired,
  darkMode: PropTypes.bool.isRequired,
  className: PropTypes.string
};

export default MainContent;
