import { createContext, useContext, useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import { es } from './locales/es';
import { en } from './locales/en';

// Traducciones disponibles
const translations = {
  es,
  en
};

// Crear el contexto
const LanguageContext = createContext();

// Hook personalizado para usar el contexto de idioma
export const useLanguage = () => {
  const context = useContext(LanguageContext);
  if (!context) {
    throw new Error('useLanguage must be used within a LanguageProvider');
  }
  return context;
};

// Función helper para obtener texto anidado usando dot notation
const getNestedTranslation = (obj, path) => {
  return path.split('.').reduce((current, key) => {
    return current && current[key] !== undefined ? current[key] : null;
  }, obj);
};

// Provider del contexto de idioma
export const LanguageProvider = ({ children }) => {
  // Obtener idioma guardado del localStorage o usar español por defecto
  const [language, setLanguage] = useState(() => {
    const savedLanguage = localStorage.getItem('quantum-language');
    return savedLanguage && translations[savedLanguage] ? savedLanguage : 'es';
  });

  // Guardar idioma en localStorage cuando cambie
  useEffect(() => {
    localStorage.setItem('quantum-language', language);
  }, [language]);

  // Función para cambiar idioma
  const changeLanguage = (newLanguage) => {
    if (translations[newLanguage]) {
      setLanguage(newLanguage);
    } else {
      console.warn(`Language '${newLanguage}' not available. Available languages:`, Object.keys(translations));
    }
  };

  // Función para obtener traducción usando dot notation
  // Ejemplo: t('auth.login') -> "Iniciar Sesión" (en español)
  const t = (key, fallback = key) => {
    const currentTranslations = translations[language];
    const translation = getNestedTranslation(currentTranslations, key);
    
    if (translation !== null) {
      return translation;
    }

    // Si no encuentra la traducción, intentar en inglés como fallback
    if (language !== 'en') {
      const englishTranslation = getNestedTranslation(translations.en, key);
      if (englishTranslation !== null) {
        console.warn(`Translation missing for '${key}' in '${language}', using English fallback`);
        return englishTranslation;
      }
    }

    // Si no encuentra en ningún idioma, usar el fallback o la key
    console.warn(`Translation missing for '${key}' in all languages`);
    return fallback;
  };

  // Función para obtener todas las traducciones de una sección
  // Ejemplo: getSection('auth') -> { login: "Iniciar Sesión", email: "Correo Electrónico", ... }
  const getSection = (sectionKey) => {
    const currentTranslations = translations[language];
    return getNestedTranslation(currentTranslations, sectionKey) || {};
  };

  // Función para verificar si un idioma está disponible
  const isLanguageAvailable = (lang) => {
    return translations.hasOwnProperty(lang);
  };

  // Función para obtener lista de idiomas disponibles
  const getAvailableLanguages = () => {
    return Object.keys(translations);
  };

  // Valor del contexto
  const contextValue = {
    language,
    changeLanguage,
    t,
    getSection,
    isLanguageAvailable,
    getAvailableLanguages,
    availableLanguages: Object.keys(translations)
  };

  return (
    <LanguageContext.Provider value={contextValue}>
      {children}
    </LanguageContext.Provider>
  );
};

LanguageProvider.propTypes = {
  children: PropTypes.node.isRequired
};

export default LanguageContext;
