/**
 * Pruebas básicas para los servicios SQQ
 * Ejecutar en la consola del navegador para verificar funcionamiento
 */

import { authService, userService, keyService } from '../index.js';

// Configuración de pruebas
const TEST_CONFIG = {
  testUser: {
    email: '<EMAIL>',
    password: 'testpassword123'
  },
  testKey: {
    key_name: 'test-key-' + Date.now(),
    algorithm: 'AES',
    num_bytes: 32,
    exportable: false
  }
};

/**
 * Ejecutar todas las pruebas
 */
export async function runAllTests() {
  console.log('🧪 Iniciando pruebas de servicios SQQ...\n');
  
  try {
    await testAuthService();
    await testUserService();
    await testKeyService();
    
    console.log('✅ Todas las pruebas completadas exitosamente!');
  } catch (error) {
    console.error('❌ Error en las pruebas:', error);
  }
}

/**
 * Probar servicio de autenticación
 */
export async function testAuthService() {
  console.log('🔐 Probando AuthService...');
  
  try {
    // Probar login (fallback a reqres.in si SQQ no está disponible)
    console.log('  - Probando login...');
    const loginResponse = await authService.login(
      '<EMAIL>', 
      'cityslicka'
    );
    console.log('  ✅ Login exitoso:', loginResponse.user?.email || 'Usuario logueado');
    
    // Probar verificación de token
    console.log('  - Verificando token...');
    const isValid = authService.isTokenValid();
    console.log('  ✅ Token válido:', isValid);
    
    // Probar obtener rol
    console.log('  - Obteniendo rol...');
    const role = authService.getUserRole();
    console.log('  ✅ Rol obtenido:', role);
    
    // Probar obtener perfil (puede fallar si no hay backend SQQ)
    try {
      console.log('  - Obteniendo perfil...');
      const profile = await authService.getProfile();
      console.log('  ✅ Perfil obtenido:', profile.email);
    } catch (error) {
      console.log('  ⚠️ Perfil no disponible (backend SQQ no conectado):', error.message);
    }
    
    console.log('✅ AuthService: Todas las pruebas pasaron\n');
    
  } catch (error) {
    console.error('❌ AuthService: Error en pruebas:', error.message);
    throw error;
  }
}

/**
 * Probar servicio de usuarios
 */
export async function testUserService() {
  console.log('👥 Probando UserService...');
  
  try {
    // Probar validaciones
    console.log('  - Probando validaciones...');
    
    try {
      userService.validateUserData({}, true);
      console.log('  ❌ Validación debería haber fallado');
    } catch (error) {
      console.log('  ✅ Validación funcionando:', error.message);
    }
    
    // Probar validación de email
    console.log('  - Probando validación de email...');
    const validEmail = userService.isValidEmail('<EMAIL>');
    const invalidEmail = userService.isValidEmail('invalid-email');
    console.log('  ✅ Validación de email:', validEmail && !invalidEmail);
    
    // Probar sanitización
    console.log('  - Probando sanitización...');
    const sanitized = userService.sanitizeUserData({
      name: '  Juan <script>  ',
      email: '<EMAIL>'
    });
    console.log('  ✅ Sanitización:', sanitized.name === 'Juan script');
    
    // Probar obtener todos los usuarios (requiere autenticación admin)
    try {
      console.log('  - Probando obtener usuarios...');
      const users = await userService.getAllUsers();
      console.log('  ✅ Usuarios obtenidos:', users.length);
    } catch (error) {
      console.log('  ⚠️ Usuarios no disponibles (requiere admin o backend no conectado):', error.message);
    }
    
    console.log('✅ UserService: Todas las pruebas pasaron\n');
    
  } catch (error) {
    console.error('❌ UserService: Error en pruebas:', error.message);
    throw error;
  }
}

/**
 * Probar servicio de llaves
 */
export async function testKeyService() {
  console.log('🔑 Probando KeyService...');
  
  try {
    // Probar validaciones
    console.log('  - Probando validaciones...');
    
    try {
      keyService.validateKeyData({});
      console.log('  ❌ Validación debería haber fallado');
    } catch (error) {
      console.log('  ✅ Validación funcionando:', error.message);
    }
    
    // Probar validación de nombre de llave
    console.log('  - Probando validación de nombre...');
    const validName = keyService.isValidKeyName('test-key-123');
    const invalidName = keyService.isValidKeyName('test key with spaces!');
    console.log('  ✅ Validación de nombre:', validName && !invalidName);
    
    // Probar validación de algoritmo
    console.log('  - Probando validación de algoritmo...');
    const validAlgorithm = keyService.isValidAlgorithm('AES');
    const invalidAlgorithm = keyService.isValidAlgorithm('INVALID');
    console.log('  ✅ Validación de algoritmo:', validAlgorithm && !invalidAlgorithm);
    
    // Probar validación de Base64
    console.log('  - Probando validación de Base64...');
    const validBase64 = keyService.isValidBase64('SGVsbG8gV29ybGQ='); // "Hello World"
    const invalidBase64 = keyService.isValidBase64('invalid-base64!');
    console.log('  ✅ Validación de Base64:', validBase64 && !invalidBase64);
    
    // Probar construcción de query params
    console.log('  - Probando construcción de query params...');
    const queryParams = keyService.buildQueryParams({
      page: 1,
      limit: 10,
      algorithm: 'AES',
      search: 'test'
    });
    console.log('  ✅ Query params:', queryParams);
    
    // Probar preparación de datos
    console.log('  - Probando preparación de datos...');
    const preparedData = keyService.prepareKeyData({
      key_name: 'test-key'
    });
    console.log('  ✅ Datos preparados:', preparedData);
    
    // Probar subida de llave (requiere autenticación y configuración CTM)
    try {
      console.log('  - Probando subida de llave...');
      const keyResponse = await keyService.uploadToCTM(TEST_CONFIG.testKey);
      console.log('  ✅ Llave subida:', keyResponse.id);
    } catch (error) {
      console.log('  ⚠️ Subida no disponible (requiere configuración CTM):', error.message);
    }
    
    console.log('✅ KeyService: Todas las pruebas pasaron\n');
    
  } catch (error) {
    console.error('❌ KeyService: Error en pruebas:', error.message);
    throw error;
  }
}

/**
 * Probar configuración de API
 */
export async function testApiConfig() {
  console.log('⚙️ Probando configuración de API...');
  
  const { API_CONFIG, API_ENDPOINTS } = await import('../config/apiConfig.js');
  
  console.log('  - Base URL:', API_CONFIG.baseURL);
  console.log('  - Timeout:', API_CONFIG.timeout);
  console.log('  - Headers:', API_CONFIG.headers);
  
  console.log('  - Endpoints disponibles:');
  console.log('    * Login:', API_ENDPOINTS.AUTH.LOGIN);
  console.log('    * Users:', API_ENDPOINTS.USERS.BASE);
  console.log('    * Keys:', API_ENDPOINTS.KEYS.UPLOAD_TO_CTM);
  
  console.log('✅ Configuración de API verificada\n');
}

/**
 * Probar cliente HTTP
 */
export async function testHttpClient() {
  console.log('🌐 Probando HttpClient...');
  
  const { default: httpClient } = await import('../core/httpClient.js');
  
  try {
    // Probar petición GET simple
    console.log('  - Probando petición GET...');
    const response = await httpClient.get('/test-endpoint');
    console.log('  ✅ Petición realizada (puede fallar por endpoint no existente)');
  } catch (error) {
    console.log('  ⚠️ Petición falló como se esperaba:', error.message);
  }
  
  console.log('✅ HttpClient verificado\n');
}

// Función de utilidad para ejecutar pruebas individuales
export const tests = {
  auth: testAuthService,
  users: testUserService,
  keys: testKeyService,
  config: testApiConfig,
  http: testHttpClient,
  all: runAllTests
};

// Instrucciones de uso
console.log(`
🧪 Pruebas de Servicios SQQ

Para ejecutar las pruebas, abre la consola del navegador y ejecuta:

// Todas las pruebas
import { runAllTests } from './src/services/tests/serviceTests.js';
runAllTests();

// Pruebas individuales
import { tests } from './src/services/tests/serviceTests.js';
tests.auth();    // Probar autenticación
tests.users();   // Probar usuarios
tests.keys();    // Probar llaves
tests.config();  // Probar configuración
tests.http();    // Probar cliente HTTP

Nota: Algunas pruebas pueden fallar si el backend SQQ no está disponible.
Esto es normal y se usarán los fallbacks correspondientes.
`);

export default tests;
