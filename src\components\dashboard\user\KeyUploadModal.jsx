import { useState } from 'react';
import { Upload } from 'lucide-react';
import PropTypes from 'prop-types';
import { useLanguage } from '../../../i18n/LanguageContext';
import { Modal, Button, FormField } from '../../common';

/**
 * Modal para subir nueva llave a CTM
 */
const KeyUploadModal = ({ isOpen, onClose, onUpload, darkMode }) => {
  const { t } = useLanguage();
  const [formData, setFormData] = useState({
    key_name: '',
    algorithm: 'AES',
    num_bytes: 32,
    exportable: false
  });
  const [isUploading, setIsUploading] = useState(false);

  const handleChange = (field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };



  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsUploading(true);
    try {
      await onUpload(formData);
      // Reset form
      setFormData({
        key_name: '',
        algorithm: 'AES',
        num_bytes: 32,
        exportable: false
      });
    } finally {
      setIsUploading(false);
    }
  };

  const algorithmOptions = [
    { value: 'AES', label: 'AES' },
    { value: 'RSA', label: 'RSA' },
    { value: 'HMAC', label: 'HMAC' }
  ];

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={
        <div className="flex items-center gap-4">
          <div className="p-3 rounded-xl bg-gradient-to-br from-blue-500 to-indigo-600 shadow-lg">
            <Upload size={24} className="text-white" />
          </div>
          <div>
            <h3 className="text-2xl font-light tracking-wide leading-relaxed text-gray-900 dark:text-white">
              {t('keys.uploadModalTitle')}
            </h3>
            <p className="text-sm font-light tracking-wide text-gray-600 dark:text-gray-400">
              {t('keys.uploadModalSubtitle')}
            </p>
          </div>
        </div>
      }
      maxWidth="max-w-2xl"
      darkMode={darkMode}
    >
      <form onSubmit={handleSubmit} className="space-y-8">
        {/* Sección de información básica */}
        <div className={`p-6 rounded-2xl border-2 shadow-lg ${
          darkMode
            ? 'bg-gradient-to-br from-gray-800 to-gray-700 border-gray-600'
            : 'bg-gradient-to-br from-white to-gray-50 border-gray-200'
        }`}>
          <h4 className="text-lg font-light tracking-wide text-gray-900 dark:text-white mb-6 flex items-center gap-3 border-b border-gray-200 dark:border-gray-600 pb-3">
            <span className="text-blue-600 dark:text-blue-400">🔑</span>
            {t('keys.keyInformation')}
          </h4>

          <div className="space-y-6">
            <div>
              <FormField
                label={t('keys.keyNameLabel')}
                type="text"
                value={formData.key_name}
                onChange={(e) => handleChange('key_name', e.target.value)}
                placeholder="mi-llave-encriptacion-001"
                required
                disabled={isUploading}
                darkMode={darkMode}
              />
              <p className="text-xs font-light tracking-wide text-gray-500 dark:text-gray-400 mt-2 flex items-center gap-2">
                <span className="text-blue-500">ℹ️</span>
                {t('keys.keyNameHelp')}
              </p>
            </div>
          </div>
        </div>

        {/* Sección de configuración técnica */}
        <div className={`p-6 rounded-2xl border-2 shadow-lg ${
          darkMode
            ? 'bg-gradient-to-br from-gray-800 to-gray-700 border-gray-600'
            : 'bg-gradient-to-br from-white to-gray-50 border-gray-200'
        }`}>
          <h4 className="text-lg font-light tracking-wide text-gray-900 dark:text-white mb-6 flex items-center gap-3 border-b border-gray-200 dark:border-gray-600 pb-3">
            <span className="text-purple-600 dark:text-purple-400">⚙️</span>
            {t('keys.technicalConfiguration')}
          </h4>

          <div className="space-y-6">
            <FormField
              label={t('keys.algorithmField')}
              type="select"
              value={formData.algorithm}
              onChange={(e) => handleChange('algorithm', e.target.value)}
              options={algorithmOptions}
              disabled={isUploading}
              darkMode={darkMode}
            />

            <div>
              <FormField
                label={t('keys.bytesNumberField')}
                type="number"
                value={formData.num_bytes}
                onChange={(e) => handleChange('num_bytes', parseInt(e.target.value))}
                min="1"
                max="1024"
                disabled={isUploading}
                darkMode={darkMode}
              />
              <p className="text-xs font-light tracking-wide text-gray-500 dark:text-gray-400 mt-2 flex items-center gap-2">
                <span className="text-purple-500">📏</span>
                {t('keys.bytesHelp')}
              </p>
            </div>

            <FormField
              label={t('keys.exportableKey')}
              type="checkbox"
              value={formData.exportable}
              onChange={(e) => handleChange('exportable', e.target.checked)}
              disabled={isUploading}
              darkMode={darkMode}
            />
          </div>
        </div>

        {/* Sección de información sobre generación automática */}
        <div className={`p-6 rounded-2xl border-2 shadow-lg ${
          darkMode
            ? 'bg-gradient-to-br from-blue-900/20 to-indigo-900/20 border-blue-700'
            : 'bg-gradient-to-br from-blue-50 to-indigo-100 border-blue-200'
        }`}>
          <div className="flex items-start gap-4">
            <div className="p-3 rounded-xl bg-gradient-to-br from-blue-500 to-indigo-600 shadow-md flex-shrink-0">
              <span className="text-2xl">🔐</span>
            </div>
            <div className="flex-1">
              <h5 className="text-lg font-light tracking-wide text-blue-800 dark:text-blue-300 mb-3">
                {t('keys.automaticGeneration')}
              </h5>
              <p className="text-sm font-light tracking-wide text-blue-700 dark:text-blue-300 leading-relaxed">
                {t('keys.automaticGenerationText')}
              </p>
            </div>
          </div>
        </div>

        {/* Footer con botones centrados */}
        <div className="flex justify-center gap-4 pt-6 border-t-2 border-gray-200 dark:border-gray-600 bg-gray-50/30 dark:bg-gray-700/20 -mx-8 px-8 pb-6 rounded-b-2xl">
          <button
            type="submit"
            disabled={isUploading || !formData.key_name}
            className="px-4 py-2 rounded-xl font-light tracking-wide shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 group bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"
          >
            <div className="p-1 bg-white/20 rounded-lg group-hover:bg-white/30 transition-all duration-300">
              <Upload size={14} className={isUploading ? "animate-spin" : ""} />
            </div>
            <span>
              {isUploading ? t('keys.uploading') : t('keys.uploadToCTMButton')}
            </span>
          </button>

          <button
            type="button"
            onClick={onClose}
            disabled={isUploading}
            className="px-4 py-2 rounded-xl font-light tracking-wide border-2 border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 hover:border-gray-400 dark:hover:border-gray-500 transform hover:scale-105 transition-all duration-300 shadow-lg hover:shadow-xl disabled:opacity-50"
          >
            {t('keys.cancel')}
          </button>
        </div>
      </form>
    </Modal>
  );
};

KeyUploadModal.propTypes = {
  isOpen: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  onUpload: PropTypes.func.isRequired,
  darkMode: PropTypes.bool.isRequired
};

export default KeyUploadModal;
