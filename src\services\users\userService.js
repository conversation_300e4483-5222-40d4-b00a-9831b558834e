/**
 * User Service
 * Servicio para manejo de usuarios
 */

import httpClient from '../core/httpClient.js';
import { API_ENDPOINTS } from '../config/apiConfig.js';
import securityLogger from '../../utils/SecurityLogger.js';

class UserService {
  /**
   * Obtener todos los usuarios (Solo Admin)
   * @returns {Promise<Array>} Lista de usuarios
   */
  async getAllUsers() {
    try {
      securityLogger.logInfo('GET_ALL_USERS_REQUEST', {
        timestamp: new Date().toISOString(),
      });

      const response = await httpClient.get(API_ENDPOINTS.USERS.BASE);
      
      securityLogger.logInfo('GET_ALL_USERS_SUCCESS', {
        userCount: response.length,
        timestamp: new Date().toISOString(),
      });

      return response;
    } catch (error) {
      securityLogger.logError('GET_ALL_USERS_ERROR', {
        error: error.message,
        status: error.status,
        timestamp: new Date().toISOString(),
      });
      throw error;
    }
  }

  /**
   * Crear nuevo usuario (Solo Admin)
   * @param {Object} userData - Datos del usuario a crear
   * @returns {Promise<Object>} Usuario creado
   */
  async createUser(userData) {
    try {
      // Validar datos requeridos
      this.validateUserData(userData, true);

      // Normalizar datos para el backend (creación)
      const normalizedData = this.normalizeUserDataForCreate(userData);

      securityLogger.logInfo('CREATE_USER_REQUEST', {
        email: normalizedData.email?.substring(0, 3) + '***',
        role: normalizedData.role,
        timestamp: new Date().toISOString(),
      });

      const response = await httpClient.post(API_ENDPOINTS.USERS.BASE, normalizedData);
      
      securityLogger.logInfo('CREATE_USER_SUCCESS', {
        userId: response.id,
        email: response.email?.substring(0, 3) + '***',
        role: response.role,
        timestamp: new Date().toISOString(),
      });

      return response;
    } catch (error) {
      securityLogger.logError('CREATE_USER_ERROR', {
        email: userData.email?.substring(0, 3) + '***',
        error: error.message,
        status: error.status,
        timestamp: new Date().toISOString(),
      });
      throw error;
    }
  }

  /**
   * Actualizar usuario por ID
   * @param {string} userId - ID del usuario
   * @param {Object} updateData - Datos a actualizar
   * @returns {Promise<Object>} Usuario actualizado
   */
  async updateUser(userId, updateData) {
    try {
      // Validar que hay datos para actualizar
      if (!updateData || Object.keys(updateData).length === 0) {
        throw new Error('No hay datos para actualizar');
      }

      // Validar datos si se están actualizando
      this.validateUserData(updateData, false);

      // Normalizar datos para el backend (actualización)
      const normalizedData = this.normalizeUserDataForUpdate(updateData);

      securityLogger.logInfo('UPDATE_USER_REQUEST', {
        userId,
        fields: Object.keys(normalizedData),
        timestamp: new Date().toISOString(),
      });

      const response = await httpClient.patch(API_ENDPOINTS.USERS.BY_ID(userId), normalizedData);
      
      securityLogger.logInfo('UPDATE_USER_SUCCESS', {
        userId: response.id,
        updatedFields: Object.keys(updateData),
        timestamp: new Date().toISOString(),
      });

      return response;
    } catch (error) {
      securityLogger.logError('UPDATE_USER_ERROR', {
        userId,
        error: error.message,
        status: error.status,
        timestamp: new Date().toISOString(),
      });
      throw error;
    }
  }

  /**
   * Validar datos de usuario
   * @param {Object} userData - Datos del usuario
   * @param {boolean} isCreation - Si es creación (requiere campos obligatorios)
   */
  validateUserData(userData, isCreation = false) {
    const errors = [];

    // Validaciones para creación
    if (isCreation) {
      if (!userData.email) {
        errors.push('Email es requerido');
      }
      if (!userData.firstName) {
        errors.push('Nombre es requerido');
      }
      if (!userData.lastName) {
        errors.push('Apellido es requerido');
      }
      if (!userData.password) {
        errors.push('Contraseña es requerida');
      }
    }

    // Validaciones de formato
    if (userData.email && !this.isValidEmail(userData.email)) {
      errors.push('Formato de email inválido');
    }

    if (userData.password && !this.isValidPassword(userData.password)) {
      errors.push('La contraseña debe tener al menos 6 caracteres');
    }

    if (userData.role && !['USER', 'ADMIN', 'user', 'admin'].includes(userData.role)) {
      errors.push('Rol inválido. Debe ser USER, ADMIN, user o admin');
    }

    if (errors.length > 0) {
      throw new Error(`Datos inválidos: ${errors.join(', ')}`);
    }
  }

  /**
   * Validar formato de email
   * @param {string} email - Email a validar
   * @returns {boolean} True si es válido
   */
  isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email) && email.length <= 254 && email.length >= 5;
  }

  /**
   * Validar contraseña
   * @param {string} password - Contraseña a validar
   * @returns {boolean} True si es válida
   */
  isValidPassword(password) {
    return password.length >= 6 && password.length <= 128;
  }

  /**
   * Validar URL
   * @param {string} url - URL a validar
   * @returns {boolean} True si es válida
   */
  isValidUrl(url) {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Sanitizar datos de entrada
   * @param {Object} data - Datos a sanitizar
   * @returns {Object} Datos sanitizados
   */
  sanitizeUserData(data) {
    const sanitized = {};
    
    for (const [key, value] of Object.entries(data)) {
      if (typeof value === 'string') {
        // Remover caracteres peligrosos y espacios extra
        sanitized[key] = value.trim().replace(/[<>'"]/g, '');
      } else {
        sanitized[key] = value;
      }
    }
    
    return sanitized;
  }

  /**
   * Eliminar usuario por ID (Solo Admin)
   * @param {string} userId - ID del usuario a eliminar
   * @returns {Promise<boolean>} Confirmación de eliminación
   */
  async deleteUser(userId) {
    try {
      securityLogger.logInfo('DELETE_USER_REQUEST', {
        userId,
        timestamp: new Date().toISOString(),
      });

      // Llamada real al backend - endpoint DELETE /users/{id}
      await httpClient.delete(API_ENDPOINTS.USERS.BY_ID(userId));

      securityLogger.logInfo('DELETE_USER_SUCCESS', {
        userId,
        timestamp: new Date().toISOString(),
      });

      return true;
    } catch (error) {
      securityLogger.logError('DELETE_USER_ERROR', {
        userId,
        error: error.message,
        status: error.status,
        timestamp: new Date().toISOString(),
      });
      throw error;
    }
  }

  /**
   * Obtener usuario por ID
   * @param {string} userId - ID del usuario
   * @returns {Promise<Object>} Datos del usuario
   */
  async getUserById(userId) {
    try {
      securityLogger.logInfo('GET_USER_BY_ID_REQUEST', {
        userId,
        timestamp: new Date().toISOString(),
      });

      const response = await httpClient.get(API_ENDPOINTS.USERS.BY_ID(userId));

      securityLogger.logInfo('GET_USER_BY_ID_SUCCESS', {
        userId: response.id,
        timestamp: new Date().toISOString(),
      });

      return response;
    } catch (error) {
      securityLogger.logError('GET_USER_BY_ID_ERROR', {
        userId,
        error: error.message,
        status: error.status,
        timestamp: new Date().toISOString(),
      });
      throw error;
    }
  }

  /**
   * Normalizar datos del usuario para creación
   * @param {Object} userData - Datos del usuario
   * @returns {Object} Datos normalizados
   */
  normalizeUserDataForCreate(userData) {
    // Campos permitidos para crear usuario
    const allowedFields = [
      'email',
      'firstName',
      'lastName',
      'company',
      'password',
      'role',
      'ctmIpAddress',
      'ctmUsername',
      'ctmPassword',
      'ctmDomain',
      'seqrngIpAddress',
      'seqrngApiToken'
    ];

    return this.filterAndNormalizeFields(userData, allowedFields);
  }

  /**
   * Normalizar datos del usuario para actualización
   * @param {Object} userData - Datos del usuario
   * @returns {Object} Datos normalizados
   */
  normalizeUserDataForUpdate(userData) {
    // Campos permitidos para actualizar usuario (sin campos de solo lectura)
    const allowedFields = [
      'email',
      'firstName',
      'lastName',
      'company',
      'role',
      'ctmIpAddress',
      'ctmUsername',
      'ctmPassword',
      'ctmDomain',
      'seqrngIpAddress',
      'seqrngApiToken'
    ];

    return this.filterAndNormalizeFields(userData, allowedFields);
  }

  /**
   * Filtrar y normalizar campos
   * @param {Object} userData - Datos del usuario
   * @param {Array} allowedFields - Campos permitidos
   * @returns {Object} Datos normalizados
   */
  filterAndNormalizeFields(userData, allowedFields) {
    // Filtrar solo los campos permitidos
    const normalized = {};
    allowedFields.forEach(field => {
      if (Object.prototype.hasOwnProperty.call(userData, field)) {
        // Solo incluir el campo si tiene un valor definido
        const value = userData[field];
        if (value !== undefined) {
          normalized[field] = value;
        }
      }
    });

    // Normalizar rol a minúsculas para el backend
    if (normalized.role) {
      normalized.role = normalized.role.toLowerCase();
    }

    // Limpiar campos vacíos (convertir strings vacíos a null)
    Object.keys(normalized).forEach(key => {
      if (normalized[key] === '') {
        normalized[key] = null;
      }
    });

    return normalized;
  }
}

// Exportar instancia singleton
const userService = new UserService();
export default userService;
