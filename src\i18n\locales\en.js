export const en = {
  // Navigation and menus
  navigation: {
    dashboard: "Dashboard",
    users: "Users",
    keys: "My Keys",
    profile: "My Profile",
    logout: "Logout"
  },

  // Authentication
  auth: {
    login: "Login",
    email: "Email",
    password: "Password",
    loginButton: "Sign In",
    loginError: "Login error",
    invalidCredentials: "Invalid credentials",
    welcome: "Welcome",
    logout: "Logout",
    logoutConfirm: "Are you sure you want to logout?"
  },

  // Dashboard
  dashboard: {
    welcome: "Welcome",
    adminWelcome: "Welcome, Quantum Administrator",
    userWelcome: "Welcome",
    adminSubtitle: "QRNG Quantum System - Administration Panel",
    userSubtitle: "Here you can manage your quantum keys and update your personal information.",
    totalUsers: "Total Users",
    activeKeys: "Active Keys",
    activeUsers: "Active Users",
    myKeys: "My Keys",
    generatedKeys: "Generated quantum keys.",
    workingKeys: "Working keys.",
    lastLogin: "Last login",
    // Mini statistics
    totalKeys: "Total Keys",
    successful: "Successful",
    failed: "Failed",
    inCTM: "In CTM",
    successRate: "% Success"
  },

  // Users
  users: {
    title: "User Management",
    createUser: "Add User",
    editUser: "Edit User",
    deleteUser: "Delete User",
    userDetails: "User Details",
    firstName: "First Name",
    lastName: "Last Name",
    email: "Email",
    company: "Company",
    role: "Role",
    status: "Status",
    active: "Active",
    inactive: "Inactive",
    admin: "Admin",
    user: "User",
    actions: "Actions",
    edit: "Edit",
    delete: "Delete",
    viewKeys: "View Keys",
    save: "Save",
    cancel: "Cancel",
    saving: "Saving...",
    creating: "Creating User...",
    createUserTitle: "Create New User",
    editUserTitle: "Edit User",
    deleteConfirm: "Are you sure you want to delete this user?",
    deleteWarning: "This action is irreversible",
    userDeleted: "User deleted successfully",
    userCreated: "User created successfully",
    userUpdated: "User updated successfully"
  },

  // Keys
  keys: {
    title: "My Quantum Keys",
    uploadKey: "Upload New Key",
    uploadToCTM: "Upload to CTM",
    uploading: "Uploading to CTM...",
    keyName: "Key Name",
    algorithm: "Algorithm",
    numBytes: "Number of Bytes",
    exportable: "Exportable",
    status: "Status",
    active: "Active",
    failed: "Failed",
    pending: "Pending",
    created: "Created",
    actions: "Actions",
    viewDetail: "View Detail",
    delete: "Delete",
    deleteKey: "Delete Key",
    deleteConfirm: "Are you sure you want to delete the key?",
    deleteWarning: "This action cannot be undone. The key will be permanently deleted from the system.",
    keyDeleted: "Key deleted successfully",
    keyUploaded: "Key uploaded successfully",
    noKeys: "You have no registered keys. Create your first key!",
    uploadNewKey: "Upload New Key to CTM",
    generateSecureKey: "Generate a new secure quantum key",
    subtitle: "Manage your quantum keys, view details and upload new keys to the CTM system.",
    loadingKeys: "Loading keys...",
    createdLabel: "Created:",
    algorithmLabel: "Algorithm:",
    // Upload key modal
    uploadModalTitle: "Upload New Key to CTM",
    uploadModalSubtitle: "Generate a new secure quantum key",
    keyInformation: "Key Information",
    keyNameLabel: "Key Name",
    keyNameHelp: "Only letters, numbers, hyphens and underscores",
    technicalConfiguration: "Technical Configuration",
    algorithmField: "Algorithm",
    bytesNumberField: "Number of Bytes",
    bytesHelp: "Between 1 and 1024 bytes",
    exportableKey: "Exportable key",
    automaticGeneration: "Automatic Key Generation",
    automaticGenerationText: "Your key's cryptographic material will be automatically generated securely using quantum algorithms. You don't need to provide any additional material.",
    uploadToCTMButton: "Upload to CTM",
    cancel: "Cancel",
    // Specific detail fields
    name: "Name",
    ctmKeyId: "CTM Key ID",
    localId: "Local ID",
    algorithmType: "Type/Algorithm",
    bytesNumber: "Number of Bytes",
    activeInCTM: "Active in CTM",
    keyBeingProcessed: "The key is being processed and validated in the system"
  },

  // Profile
  profile: {
    title: "My Profile",
    editProfile: "Edit Profile",
    changePassword: "Change Password",
    personalInfo: "Personal Information",
    currentPassword: "Current Password",
    newPassword: "New Password",
    confirmPassword: "Confirm New Password",
    updateProfile: "Update Profile",
    updating: "Updating...",
    changing: "Changing...",
    profileUpdated: "Profile updated successfully",
    passwordChanged: "Password changed successfully",
    serviceConfig: "Service Configuration",
    serviceConfigNote: "CipherTrust Manager and SEQRNG configuration is managed by the system administrator. If you need to modify these settings, contact your administrator.",
    subtitle: "Manage your personal information and account settings.",
    // Additional fields
    fullName: "Full Name",
    firstName: "First Name",
    lastName: "Last Name",
    email: "Email",
    company: "Company",
    registrationDate: "Registration Date",
    notSpecified: "Not specified",
    registrationDate: "Registration Date",
    identityVerification: "Identity Verification",
    securityRequirements: "Security Requirements",
    minimumCharacters: "Minimum 8 characters",
    includeUppercase: "Include uppercase",
    includeLowercase: "Include lowercase",
    includeNumbers: "Include numbers",
    contactInformation: "Contact Information"
  },

  // Modals
  modals: {
    close: "Close",
    save: "Save",
    cancel: "Cancel",
    confirm: "Confirm",
    delete: "Delete",
    edit: "Edit",
    create: "Create",
    upload: "Upload",
    loading: "Loading...",
    saving: "Saving...",
    deleting: "Deleting...",
    uploading: "Uploading...",
    confirmDeletion: "Confirm Deletion",
    irreversibleAction: "This action is irreversible",
    deleteKey: "Delete Key",
    keyDetail: "Key Detail",
    basicInformation: "Basic Information",
    changePassword: "Change Password",
    editProfile: "Edit My Profile",
    personalInformation: "Personal Information",
    contactInformation: "Contact Information",
    serviceConfiguration: "Service Configuration"
  },

  // Key details
  keyDetails: {
    title: "Key Detail",
    basicInfo: "Basic Information",
    name: "Name",
    ctmKeyId: "CTM Key ID",
    localId: "Local ID",
    type: "Type/Algorithm",
    numBytes: "Number of Bytes",
    exportable: "Exportable",
    yes: "Yes",
    no: "No",
    status: "Status",
    statusActive: "The key is active and working perfectly in CipherTrust Manager",
    statusFailed: "There was a problem uploading the key to CipherTrust Manager",
    statusPending: "The key is being processed",
    dates: "Dates",
    created: "Creation",
    updated: "Updated",
    uploaded: "Uploaded to CTM",
    entropyReport: "Auto-certification Report",
    entropyStatus: "Entropy Status",
    quantumFidelity: "Quantum Fidelity",
    source: "Source",
    error: "Error",
    keyMaterial: "Key Material",
    keyMaterialWarning: "⚠️ This information is sensitive and should be handled with care",
    copiedToClipboard: "copied to clipboard",
    copyError: "Error copying to clipboard",
    keyMaterialWarning: "⚠️ This information is sensitive and should be handled with care",
    copy: "Copy",
    copied: "Copied to clipboard"
  },

  // Notifications and messages
  notifications: {
    success: "Success",
    error: "Error",
    warning: "Warning",
    info: "Information",
    loading: "Loading...",
    noData: "No data available",
    tryAgain: "Try again",
    sessionExpired: "Session expired",
    networkError: "Connection error",
    unknownError: "Unknown error"
  },

  // Forms
  forms: {
    required: "This field is required",
    invalidEmail: "Invalid email",
    passwordTooShort: "Password must be at least 6 characters",
    passwordsNotMatch: "Passwords do not match",
    invalidFormat: "Invalid format",
    fieldRequired: "Field required"
  },

  // Status
  status: {
    active: "Active",
    inactive: "Inactive",
    pending: "Pending",
    failed: "Failed",
    success: "Success",
    loading: "Loading",
    error: "Error"
  },

  // Common buttons
  buttons: {
    save: "Save",
    cancel: "Cancel",
    delete: "Delete",
    edit: "Edit",
    create: "Create",
    upload: "Upload",
    download: "Download",
    close: "Close",
    confirm: "Confirm",
    back: "Back",
    next: "Next",
    previous: "Previous",
    refresh: "Refresh",
    search: "Search",
    filter: "Filter",
    clear: "Clear",
    submit: "Submit"
  },

  // Footer
  footer: {
    company: "Secure Quantum Technology",
    copyright: "© 2025",
    systemName: "Quantum Login System"
  },

  // Languages
  languages: {
    spanish: "Spanish",
    english: "English",
    es: "ES Esp",
    en: "US EN"
  },

  // Sidebar
  sidebar: {
    superAdmin: "Super Admin",
    logout: "Logout",
    logoutConfirm: "Are you sure you want to logout?"
  },

  // Validations
  validation: {
    required: "This field is required",
    invalidEmail: "Invalid email",
    passwordTooShort: "Password must be at least 6 characters",
    passwordsNotMatch: "Passwords do not match"
  },

  // Loading messages
  loading: {
    users: "Loading users...",
    keys: "Loading keys...",
    stats: "Loading statistics...",
    saving: "Saving...",
    deleting: "Deleting...",
    uploading: "Uploading..."
  }
};
