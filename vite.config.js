import { defineConfig, loadEnv } from 'vite'
import react from '@vitejs/plugin-react'

// Plugin personalizado para reemplazar variables de entorno en index.html
const htmlEnvReplace = (env) => {
  return {
    name: 'html-env-replace',
    enforce: 'post',
    transformIndexHtml(html, context) {
      const apiUrl = env.VITE_API_BASE_URL || 'http://localhost:3000'
      console.log('🔧 Debug:', { env, apiUrl })

      // Reemplazar variables de entorno en el HTML
      const result = html.replace(
        /%VITE_API_BASE_URL%/g,
        apiUrl
      )

      console.log('🔧 Replacement:', {
        before: html.includes('%VITE_API_BASE_URL%') ? 'has variable' : 'no variable',
        after: result.substring(result.indexOf('connect-src'), result.indexOf('connect-src') + 80)
      })

      return result
    }
  }
}

// https://vite.dev/config/
export default defineConfig(({ mode }) => {
  // Cargar variables de entorno
  const env = loadEnv(mode, process.cwd(), 'VITE_')

  return {
    plugins: [react(), htmlEnvReplace(env)],

    // 🛡️ Configuración de seguridad para desarrollo
    server: {
      headers: {
        // Previene clickjacking
        'X-Frame-Options': 'DENY',

        // Previene MIME type sniffing
        'X-Content-Type-Options': 'nosniff',

        // Controla información del referrer
        'Referrer-Policy': 'strict-origin-when-cross-origin',

        // Deshabilita APIs peligrosas
        'Permissions-Policy': 'camera=(), microphone=(), geolocation=(), payment=(), usb=(), magnetometer=(), gyroscope=(), accelerometer=()',

        // Previene DNS prefetching
        'X-DNS-Prefetch-Control': 'off',

        // Fuerza HTTPS en producción (solo para desarrollo local)
        'Strict-Transport-Security': 'max-age=31536000; includeSubDomains',

        // Headers adicionales de seguridad
        'X-XSS-Protection': '1; mode=block',
        'X-Permitted-Cross-Domain-Policies': 'none',
        'Cross-Origin-Embedder-Policy': 'require-corp',
        'Cross-Origin-Opener-Policy': 'same-origin',
        'Cross-Origin-Resource-Policy': 'same-origin'
      }
    },

    // 🔒 Configuración de build para producción
    build: {
      // Minificar para ofuscar código
      minify: 'terser',
      terserOptions: {
        compress: {
          drop_console: true, // Remover console.logs en producción
          drop_debugger: true // Remover debugger statements
        }
      },

      // Generar source maps solo en desarrollo
      sourcemap: false,

      // Configuración de chunks para mejor seguridad
      rollupOptions: {
        output: {
          // Ofuscar nombres de archivos
          entryFileNames: 'assets/[name]-[hash].js',
          chunkFileNames: 'assets/[name]-[hash].js',
          assetFileNames: 'assets/[name]-[hash].[ext]'
        }
      }
    }
  }
})
