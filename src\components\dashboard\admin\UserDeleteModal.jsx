import PropTypes from 'prop-types';
import { Trash2 } from 'lucide-react';
import { Modal } from '../../common';

/**
 * Modal de confirmación para eliminar usuario
 */
const UserDeleteModal = ({ isOpen, user, onClose, onConfirm, darkMode }) => {
  if (!user) return null;

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title="Eliminar Usuario"
      maxWidth="max-w-md"
      darkMode={darkMode}
    >
      {/* Contenido del modal */}
      <div className="text-center mb-8">
        {/* Icono de advertencia */}
        <div className="mx-auto flex items-center justify-center w-16 h-16 bg-red-100 dark:bg-red-900/30 rounded-full mb-4">
          <Trash2 size={32} className="text-red-600 dark:text-red-400" />
        </div>

        {/* Mensaje principal */}
        <h3 className="text-lg font-light tracking-wide leading-relaxed mb-3 text-gray-900 dark:text-white">
          ¿Eliminar Usuario?
        </h3>

        <p className="font-light tracking-wide mb-2 text-gray-700 dark:text-gray-300">
          ¿Estás seguro de que deseas eliminar al usuario
        </p>
        <p className="font-medium text-lg text-red-600 dark:text-red-400 mb-4">
          {user.firstName} {user.lastName}
        </p>

        <div className="p-4 bg-red-50 dark:bg-red-900/20 rounded-xl border border-red-200 dark:border-red-800">
          <p className="text-sm font-light tracking-wide text-red-700 dark:text-red-300 flex items-center justify-center gap-2">
            <span className="text-red-500">⚠️</span>
            Esta acción no se puede deshacer
          </p>
        </div>
      </div>

      {/* Footer con botones centrados */}
      <div className="flex justify-center gap-4 pt-6 border-t-2 border-gray-200 dark:border-gray-600 bg-gray-50/30 dark:bg-gray-700/20 -mx-8 px-8 pb-6 rounded-b-2xl">
        <button
          onClick={onConfirm}
          className="px-8 py-3 rounded-xl font-light tracking-wide shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 bg-gradient-to-r from-red-600 to-red-700 hover:from-red-700 hover:to-red-800 text-white flex items-center gap-3"
        >
          <div className="p-1 bg-white/20 rounded-lg">
            <Trash2 size={16} />
          </div>
          <span>
            Eliminar Usuario
          </span>
        </button>

        <button
          onClick={onClose}
          className="px-8 py-3 rounded-xl font-light tracking-wide border-2 border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 hover:border-gray-400 dark:hover:border-gray-500 transform hover:scale-105 transition-all duration-300"
        >
          Cancelar
        </button>
      </div>
    </Modal>
  );
};

UserDeleteModal.propTypes = {
  isOpen: PropTypes.bool.isRequired,
  user: PropTypes.object,
  onClose: PropTypes.func.isRequired,
  onConfirm: PropTypes.func.isRequired,
  darkMode: PropTypes.bool.isRequired
};

export default UserDeleteModal;
