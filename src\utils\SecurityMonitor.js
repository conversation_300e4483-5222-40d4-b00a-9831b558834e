// 🕵️ SecurityMonitor - Detección de comportamiento sospechoso

class SecurityMonitor {
  constructor() {
    this.isActive = false;
    this.isLoginMode = false; // Modo especial para login
    this.metrics = {
      clickCount: 0,
      keystrokes: 0,
      rapidClicks: 0,
      suspiciousActivity: 0,
      sessionStart: Date.now(),
      lastActivity: Date.now()
    };

    // Umbrales normales
    this.thresholds = {
      maxClicksPerSecond: 10,
      maxKeystrokesPerSecond: 20,
      maxRapidClickSequences: 3,
      inactivityTimeout: 30 * 60 * 1000, // 30 minutos
      maxSuspiciousActivities: 5
    };

    // Umbrales más relajados para modo login
    this.loginThresholds = {
      maxClicksPerSecond: 20, // Más permisivo
      maxKeystrokesPerSecond: 40, // Más permisivo
      maxRapidClickSequences: 8, // Más tolerante
      inactivityTimeout: 60 * 60 * 1000, // 1 hora
      maxSuspiciousActivities: 15 // Mucho más tolerante
    };

    this.intervals = [];
    this.eventListeners = [];
  }

  // Obtener umbrales según el modo actual
  getCurrentThresholds() {
    return this.isLoginMode ? this.loginThresholds : this.thresholds;
  }

  // Iniciar monitoreo en modo login (menos agresivo)
  startLoginMode() {
    console.log('🔒 SecurityMonitor: Iniciando en modo LOGIN (menos sensible)');
    this.isLoginMode = true;
    this.start();
  }

  // Cambiar a modo normal (después de login exitoso)
  switchToNormalMode() {
    console.log('🔒 SecurityMonitor: Cambiando a modo NORMAL (más estricto)');
    this.isLoginMode = false;
    // Resetear métricas para empezar limpio
    this.metrics.suspiciousActivity = 0;
    this.metrics.clickCount = 0;
    this.metrics.keystrokes = 0;
  }

  // Iniciar monitoreo normal
  start() {
    if (this.isActive) return;
    
    this.isActive = true;
    console.log('[SECURITY_MONITOR] Starting security monitoring...');
    
    this.setupEventListeners();
    this.startPeriodicChecks();
    
    // Log inicial
    this.logSecurityEvent('MONITORING_STARTED', {
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      viewport: `${window.innerWidth}x${window.innerHeight}`
    });
  }

  // Detener monitoreo
  stop() {
    if (!this.isActive) return;
    
    this.isActive = false;
    console.log('[SECURITY_MONITOR] Stopping security monitoring...');
    
    // Limpiar event listeners
    this.eventListeners.forEach(({ element, event, handler }) => {
      element.removeEventListener(event, handler);
    });
    this.eventListeners = [];
    
    // Limpiar intervalos
    this.intervals.forEach(interval => clearInterval(interval));
    this.intervals = [];
    
    this.logSecurityEvent('MONITORING_STOPPED', {
      sessionDuration: Date.now() - this.metrics.sessionStart,
      totalClicks: this.metrics.clickCount,
      totalKeystrokes: this.metrics.keystrokes,
      suspiciousActivities: this.metrics.suspiciousActivity
    });
  }

  // Configurar event listeners
  setupEventListeners() {
    // Monitorear clicks
    const clickHandler = (e) => this.handleClick(e);
    document.addEventListener('click', clickHandler);
    this.eventListeners.push({ element: document, event: 'click', handler: clickHandler });

    // Monitorear teclas
    const keyHandler = (e) => this.handleKeypress(e);
    document.addEventListener('keydown', keyHandler);
    this.eventListeners.push({ element: document, event: 'keydown', handler: keyHandler });

    // Monitorear cambios de foco (posible tab switching)
    const focusHandler = () => this.handleFocusChange();
    window.addEventListener('blur', focusHandler);
    window.addEventListener('focus', focusHandler);
    this.eventListeners.push({ element: window, event: 'blur', handler: focusHandler });
    this.eventListeners.push({ element: window, event: 'focus', handler: focusHandler });

    // Monitorear intentos de abrir DevTools
    const devToolsHandler = (e) => this.detectDevTools(e);
    document.addEventListener('keydown', devToolsHandler);
    this.eventListeners.push({ element: document, event: 'keydown', handler: devToolsHandler });

    // Monitorear cambios de tamaño (posible DevTools)
    const resizeHandler = () => this.handleResize();
    window.addEventListener('resize', resizeHandler);
    this.eventListeners.push({ element: window, event: 'resize', handler: resizeHandler });
  }

  // Manejar clicks
  handleClick(event) {
    this.metrics.clickCount++;
    this.metrics.lastActivity = Date.now();
    
    // Detectar clicks muy rápidos
    if (!this.lastClickTime) {
      this.lastClickTime = Date.now();
      this.clicksInSecond = 1;
    } else {
      const timeDiff = Date.now() - this.lastClickTime;
      
      if (timeDiff < 1000) {
        this.clicksInSecond++;
        
        if (this.clicksInSecond > this.getCurrentThresholds().maxClicksPerSecond) {
          this.reportSuspiciousActivity('RAPID_CLICKING', {
            clicksPerSecond: this.clicksInSecond,
            element: event.target.tagName,
            coordinates: `${event.clientX},${event.clientY}`
          });
        }
      } else {
        this.lastClickTime = Date.now();
        this.clicksInSecond = 1;
      }
    }
  }

  // Manejar teclas
  handleKeypress(event) {
    this.metrics.keystrokes++;
    this.metrics.lastActivity = Date.now();
    
    // Detectar tecleo muy rápido
    if (!this.lastKeypressTime) {
      this.lastKeypressTime = Date.now();
      this.keystrokesInSecond = 1;
    } else {
      const timeDiff = Date.now() - this.lastKeypressTime;
      
      if (timeDiff < 1000) {
        this.keystrokesInSecond++;
        
        if (this.keystrokesInSecond > this.getCurrentThresholds().maxKeystrokesPerSecond) {
          this.reportSuspiciousActivity('RAPID_TYPING', {
            keystrokesPerSecond: this.keystrokesInSecond,
            key: event.key
          });
        }
      } else {
        this.lastKeypressTime = Date.now();
        this.keystrokesInSecond = 1;
      }
    }
  }

  // Detectar intentos de abrir DevTools
  detectDevTools(event) {
    // F12, Ctrl+Shift+I, Ctrl+Shift+J, Ctrl+U
    if (
      event.key === 'F12' ||
      (event.ctrlKey && event.shiftKey && (event.key === 'I' || event.key === 'J')) ||
      (event.ctrlKey && event.key === 'U')
    ) {
      this.reportSuspiciousActivity('DEVTOOLS_ATTEMPT', {
        key: event.key,
        ctrlKey: event.ctrlKey,
        shiftKey: event.shiftKey
      });
    }
  }

  // Manejar cambios de foco
  handleFocusChange() {
    this.logSecurityEvent('FOCUS_CHANGE', {
      timestamp: new Date().toISOString(),
      documentHidden: document.hidden
    });
  }

  // Manejar cambios de tamaño (posible DevTools)
  handleResize() {
    const newSize = `${window.innerWidth}x${window.innerHeight}`;
    this.logSecurityEvent('WINDOW_RESIZE', {
      newSize: newSize,
      timestamp: new Date().toISOString()
    });
  }

  // Reportar actividad sospechosa
  reportSuspiciousActivity(type, details) {
    this.metrics.suspiciousActivity++;
    
    this.logSecurityEvent('SUSPICIOUS_ACTIVITY', {
      type: type,
      details: details,
      totalSuspiciousActivities: this.metrics.suspiciousActivity,
      timestamp: new Date().toISOString()
    });

    // Si hay demasiadas actividades sospechosas, tomar acción
    if (this.metrics.suspiciousActivity >= this.getCurrentThresholds().maxSuspiciousActivities) {
      this.handleHighRiskBehavior();
    }
  }

  // Manejar comportamiento de alto riesgo
  handleHighRiskBehavior() {
    this.logSecurityEvent('HIGH_RISK_BEHAVIOR_DETECTED', {
      suspiciousActivities: this.metrics.suspiciousActivity,
      sessionDuration: Date.now() - this.metrics.sessionStart,
      action: 'FORCE_LOGOUT'
    });

    // Forzar logout por seguridad
    alert('⚠️ Actividad sospechosa detectada. Por seguridad, se cerrará la sesión.');
    
    // Limpiar sesión
    sessionStorage.clear();
    localStorage.clear();
    
    // Redirigir al login
    window.location.href = '/';
  }

  // Verificaciones periódicas
  startPeriodicChecks() {
    // Verificar inactividad cada minuto
    const inactivityCheck = setInterval(() => {
      const inactiveTime = Date.now() - this.metrics.lastActivity;
      
      if (inactiveTime > this.getCurrentThresholds().inactivityTimeout) {
        this.logSecurityEvent('SESSION_TIMEOUT', {
          inactiveTime: inactiveTime,
          action: 'AUTO_LOGOUT'
        });
        
        // Auto logout por inactividad
        sessionStorage.clear();
        localStorage.clear();
        window.location.href = '/';
      }
    }, 60000); // Cada minuto
    
    this.intervals.push(inactivityCheck);

    // Reset de métricas cada segundo
    const metricsReset = setInterval(() => {
      this.clicksInSecond = 0;
      this.keystrokesInSecond = 0;
    }, 1000);
    
    this.intervals.push(metricsReset);
  }

  // Log de eventos de seguridad
  logSecurityEvent(event, details) {
    const logEntry = {
      event: event,
      details: details,
      timestamp: new Date().toISOString(),
      sessionId: this.getSessionId()
    };
    
    console.log(`[SECURITY_MONITOR] ${event}:`, logEntry);
    
    // En producción, esto se enviaría al backend
    // fetch('/api/security-log', { method: 'POST', body: JSON.stringify(logEntry) });
  }

  // Obtener ID de sesión único
  getSessionId() {
    if (!this.sessionId) {
      this.sessionId = 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }
    return this.sessionId;
  }

  // Obtener métricas actuales
  getMetrics() {
    return {
      ...this.metrics,
      sessionDuration: Date.now() - this.metrics.sessionStart,
      isActive: this.isActive
    };
  }
}

// Instancia singleton
const securityMonitor = new SecurityMonitor();

export default securityMonitor;
