import { useState } from 'react';
import { Lock, Eye, EyeOff, Shield, Key, CheckCircle, X } from 'lucide-react';
import PropTypes from 'prop-types';
import { useLanguage } from '../../../i18n/LanguageContext';
import { Modal, Button, FormField, ErrorAlert } from '../../common';
import { validationRules } from '../../../utils/validations';

/**
 * Modal para cambio de contraseña del usuario
 */
const PasswordChangeModal = ({ isOpen, onClose, onChangePassword, darkMode }) => {
  const { t } = useLanguage();
  const [formData, setFormData] = useState({
    currentPassword: '',
    newPassword: '',
    confirmPassword: ''
  });
  const [errors, setErrors] = useState({});
  const [showPasswords, setShowPasswords] = useState({
    current: false,
    new: false,
    confirm: false
  });
  const [isChanging, setIsChanging] = useState(false);
  const [error, setError] = useState(null);

  const handleChange = (field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Limpiar error del campo cuando el usuario empiece a escribir
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: null }));
    }
    
    // Limpiar error general
    if (error) {
      setError(null);
    }
  };

  const togglePasswordVisibility = (field) => {
    setShowPasswords(prev => ({ ...prev, [field]: !prev[field] }));
  };

  const validateForm = () => {
    const newErrors = {};

    // Validar contraseña actual
    if (!formData.currentPassword) {
      newErrors.currentPassword = 'La contraseña actual es requerida';
    }

    // Validar nueva contraseña
    const passwordError = validationRules.password(formData.newPassword);
    if (passwordError) {
      newErrors.newPassword = passwordError;
    }

    // Validar confirmación de contraseña
    if (!formData.confirmPassword) {
      newErrors.confirmPassword = 'Debe confirmar la nueva contraseña';
    } else if (formData.newPassword !== formData.confirmPassword) {
      newErrors.confirmPassword = 'Las contraseñas no coinciden';
    }

    // Validar que la nueva contraseña sea diferente a la actual
    if (formData.currentPassword && formData.newPassword && 
        formData.currentPassword === formData.newPassword) {
      newErrors.newPassword = 'La nueva contraseña debe ser diferente a la actual';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setIsChanging(true);
    setError(null);

    try {
      await onChangePassword({
        currentPassword: formData.currentPassword,
        newPassword: formData.newPassword
      });
      
      // Reset form
      setFormData({
        currentPassword: '',
        newPassword: '',
        confirmPassword: ''
      });
      setErrors({});
      
      // Cerrar modal
      onClose();
    } catch (err) {
      setError(err.message || 'Error al cambiar la contraseña');
    } finally {
      setIsChanging(false);
    }
  };

  const handleClose = () => {
    setFormData({
      currentPassword: '',
      newPassword: '',
      confirmPassword: ''
    });
    setErrors({});
    setError(null);
    setShowPasswords({
      current: false,
      new: false,
      confirm: false
    });
    onClose();
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={handleClose}
      title={
        <div className="flex items-center gap-3">
          <div className="p-2 rounded-xl bg-gradient-to-r from-red-500 to-red-600 shadow-lg">
            <Lock size={20} className="text-white" />
          </div>
          <span className="text-xl font-light tracking-wide">{t('modals.changePassword')}</span>
        </div>
      }
      maxWidth="max-w-lg"
      darkMode={darkMode}
    >
      <form onSubmit={handleSubmit} className="space-y-6">
        <ErrorAlert error={error} onClose={() => setError(null)} />

        {/* Contraseña Actual */}
        <div className={`p-6 rounded-xl border-2 ${
          darkMode
            ? 'bg-gray-700/50 border-gray-600/50'
            : 'bg-gray-50/50 border-gray-200/50'
        }`}>
          <div className="flex items-center gap-2 mb-4">
            <Shield size={18} className="text-red-500" />
            <h4 className="text-lg font-medium text-gray-900 dark:text-white">
              Verificación de Identidad
            </h4>
          </div>

          <div className="relative group">
            <FormField
              label={t('profile.currentPassword')}
              type={showPasswords.current ? "text" : "password"}
              value={formData.currentPassword}
              onChange={(e) => handleChange('currentPassword', e.target.value)}
              required
              disabled={isChanging}
              darkMode={darkMode}
              className="pr-12 transition-all duration-300 group-hover:shadow-md"
              placeholder="Ingresa tu contraseña actual"
            />
            <button
              type="button"
              onClick={() => togglePasswordVisibility('current')}
              className="absolute right-3 top-8 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 transition-colors duration-200"
              disabled={isChanging}
            >
              {showPasswords.current ? <EyeOff size={20} /> : <Eye size={20} />}
            </button>
            {errors.currentPassword && (
              <p className="text-red-500 text-sm mt-2 flex items-center gap-1">
                <div className="w-1 h-1 bg-red-500 rounded-full"></div>
                {errors.currentPassword}
              </p>
            )}
          </div>
        </div>

        {/* Nueva Contraseña */}
        <div className={`p-6 rounded-xl border-2 ${
          darkMode
            ? 'bg-gray-700/50 border-gray-600/50'
            : 'bg-gray-50/50 border-gray-200/50'
        }`}>
          <div className="flex items-center gap-2 mb-4">
            <Key size={18} className="text-blue-500" />
            <h4 className="text-lg font-medium text-gray-900 dark:text-white">
              {t('profile.newPassword')}
            </h4>
          </div>

          <div className="space-y-4">
            <div className="relative group">
              <FormField
                label={t('profile.newPassword')}
                type={showPasswords.new ? "text" : "password"}
                value={formData.newPassword}
                onChange={(e) => handleChange('newPassword', e.target.value)}
                required
                disabled={isChanging}
                darkMode={darkMode}
                className="pr-12 transition-all duration-300 group-hover:shadow-md"
                placeholder="Ingresa tu nueva contraseña"
              />
              <button
                type="button"
                onClick={() => togglePasswordVisibility('new')}
                className="absolute right-3 top-8 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 transition-colors duration-200"
                disabled={isChanging}
              >
                {showPasswords.new ? <EyeOff size={20} /> : <Eye size={20} />}
              </button>
              {errors.newPassword && (
                <p className="text-red-500 text-sm mt-2 flex items-center gap-1">
                  <div className="w-1 h-1 bg-red-500 rounded-full"></div>
                  {errors.newPassword}
                </p>
              )}
            </div>

            <div className="relative group">
              <FormField
                label={t('profile.confirmPassword')}
                type={showPasswords.confirm ? "text" : "password"}
                value={formData.confirmPassword}
                onChange={(e) => handleChange('confirmPassword', e.target.value)}
                required
                disabled={isChanging}
                darkMode={darkMode}
                className="pr-12 transition-all duration-300 group-hover:shadow-md"
                placeholder="Confirma tu nueva contraseña"
              />
              <button
                type="button"
                onClick={() => togglePasswordVisibility('confirm')}
                className="absolute right-3 top-8 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 transition-colors duration-200"
                disabled={isChanging}
              >
                {showPasswords.confirm ? <EyeOff size={20} /> : <Eye size={20} />}
              </button>
              {errors.confirmPassword && (
                <p className="text-red-500 text-sm mt-2 flex items-center gap-1">
                  <div className="w-1 h-1 bg-red-500 rounded-full"></div>
                  {errors.confirmPassword}
                </p>
              )}
            </div>
          </div>
        </div>

        {/* Información de Seguridad */}
        <div className={`p-6 rounded-xl border-2 ${
          darkMode
            ? 'bg-blue-900/20 border-blue-700/50'
            : 'bg-blue-50/50 border-blue-200/50'
        }`}>
          <div className="flex items-center gap-2 mb-3">
            <div className="p-1 rounded-lg bg-blue-500/20">
              <CheckCircle size={16} className="text-blue-600" />
            </div>
            <h5 className="font-medium text-blue-800 dark:text-blue-300">
              Requisitos de Seguridad
            </h5>
          </div>
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 text-sm text-blue-700 dark:text-blue-400">
            <div className="flex items-center gap-2">
              <div className="w-1.5 h-1.5 bg-blue-500 rounded-full"></div>
              <span>Mínimo 8 caracteres</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-1.5 h-1.5 bg-blue-500 rounded-full"></div>
              <span>Incluye mayúsculas</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-1.5 h-1.5 bg-blue-500 rounded-full"></div>
              <span>Incluye minúsculas</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-1.5 h-1.5 bg-blue-500 rounded-full"></div>
              <span>Agrega números</span>
            </div>
          </div>

          {/* Indicador de fortaleza */}
          {formData.newPassword && (
            <div className="mt-4 p-3 rounded-lg bg-blue-100/50 dark:bg-blue-800/30">
              <div className="flex items-center gap-2 text-sm">
                <Shield size={14} className="text-blue-600" />
                <span className="text-blue-800 dark:text-blue-300 font-medium">
                  Fortaleza: {formData.newPassword.length >= 8 ? 'Buena' : 'Débil'}
                </span>
              </div>
            </div>
          )}
        </div>

        {/* Botones del mismo tamaño que la foto */}
        <div className="flex gap-3 pt-4">
          <button
            type="submit"
            disabled={isChanging || !formData.currentPassword || !formData.newPassword || !formData.confirmPassword}
            className="group relative overflow-hidden px-4 py-2 rounded-xl font-light tracking-wide text-white bg-gradient-to-r from-red-500 via-red-600 to-red-700 hover:from-red-600 hover:via-red-700 hover:to-red-800 shadow-lg hover:shadow-xl hover:shadow-red-500/30 transform hover:scale-105 transition-all duration-300 border border-red-400/30 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
          >
            <div className="absolute inset-0 bg-gradient-to-r from-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
            <div className="relative flex items-center gap-2">
              {isChanging ? (
                <>
                  <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                  <span>{t('profile.changing')}</span>
                </>
              ) : (
                <>
                  <div className="p-1 rounded-lg bg-white/20 group-hover:bg-white/30 transition-all duration-300">
                    <Lock size={14} />
                  </div>
                  <span>{t('profile.changePassword')}</span>
                </>
              )}
            </div>
          </button>

          <button
            type="button"
            onClick={handleClose}
            disabled={isChanging}
            className="group relative overflow-hidden px-4 py-2 rounded-xl font-light tracking-wide bg-gray-600 hover:bg-gray-700 text-white shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 border border-gray-500/30 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
          >
            <div className="absolute inset-0 bg-gradient-to-r from-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
            <div className="relative flex items-center gap-2">
              <div className="p-1 rounded-lg bg-white/20 group-hover:bg-white/30 transition-all duration-300">
                <X size={14} />
              </div>
              <span>{t('modals.cancel')}</span>
            </div>
          </button>
        </div>
      </form>
    </Modal>
  );
};

PasswordChangeModal.propTypes = {
  isOpen: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  onChangePassword: PropTypes.func.isRequired,
  darkMode: PropTypes.bool.isRequired
};

export default PasswordChangeModal;
