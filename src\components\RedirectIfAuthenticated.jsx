// src/components/RedirectIfAuthenticated.jsx
import { Navigate } from 'react-router-dom';

// 🛡️ Gestión segura de tokens (consistente con otros componentes)
const TokenManager = {
  isTokenValid: () => {
    const token = sessionStorage.getItem('token');
    const expiry = sessionStorage.getItem('tokenExpiry');

    if (!token || !expiry) {
      // Verificar también localStorage legacy
      const legacyToken = localStorage.getItem('token');
      if (legacyToken) {
        return true; // Permitir migración en RutaProtegida
      }
      return false;
    }

    if (Date.now() > parseInt(expiry)) {
      TokenManager.clearToken();
      return false;
    }

    return true;
  },

  clearToken: () => {
    sessionStorage.removeItem('token');
    sessionStorage.removeItem('userRole');
    sessionStorage.removeItem('tokenExpiry');
    sessionStorage.removeItem('loginTime');
    localStorage.removeItem('token');
    localStorage.removeItem('userRole');
  },

  getUserRole: () => {
    return sessionStorage.getItem('userRole') || localStorage.getItem('userRole');
  }
};

const RedirectIfAuthenticated = ({ children }) => {
  const isTokenValid = TokenManager.isTokenValid();
  const userRole = TokenManager.getUserRole();

  // Función para normalizar roles (mapear backend roles a frontend roles)
  const normalizeRole = (role) => {
    if (!role) return null;
    const roleLower = role.toLowerCase();
    switch (roleLower) {
      case 'admin':
        return 'admin';
      case 'user':
        return 'usuario';
      case 'usuario':
        return 'usuario';
      default:
        return roleLower;
    }
  };

  const normalizedUserRole = normalizeRole(userRole);

  // Si el usuario ya está autenticado con token válido, redirigir a su dashboard
  if (isTokenValid && normalizedUserRole) {

    if (normalizedUserRole === 'admin') {
      return <Navigate to="/admin" replace />;
    } else if (normalizedUserRole === 'usuario') {
      return <Navigate to="/usuario" replace />;
    }
  }

  // Si no está autenticado o token expirado, mostrar el componente hijo (Login)
  return children;
};

export default RedirectIfAuthenticated;
