/**
 * Key Service
 * Servicio para manejo de llaves de encriptación
 */

import httpClient from '../core/httpClient.js';
import { API_ENDPOINTS } from '../config/apiConfig.js';
import securityLogger from '../../utils/SecurityLogger.js';

class KeyService {
  /**
   * Subir llave a CipherTrust Manager
   * @param {Object} keyData - Datos de la llave
   * @returns {Promise<Object>} Respuesta de la subida
   */
  async uploadToCTM(keyData) {
    try {
      // Validar datos de la llave
      this.validateKeyData(keyData);

      securityLogger.logInfo('UPLOAD_KEY_TO_CTM_REQUEST', {
        keyName: keyData.key_name,
        algorithm: keyData.algorithm || 'AES',
        numBytes: keyData.num_bytes || 32,
        timestamp: new Date().toISOString(),
      });

      const response = await httpClient.post(API_ENDPOINTS.KEYS.UPLOAD_TO_CTM, keyData);
      
      securityLogger.logInfo('UPLOAD_KEY_TO_CTM_SUCCESS', {
        keyId: response.id,
        keyName: response.name,
        status: response.status,
        uploadedToCtm: response.uploadedToCtm,
        timestamp: new Date().toISOString(),
      });

      return response;
    } catch (error) {
      securityLogger.logError('UPLOAD_KEY_TO_CTM_ERROR', {
        keyName: keyData.key_name,
        error: error.message,
        status: error.status,
        timestamp: new Date().toISOString(),
      });
      throw error;
    }
  }

  /**
   * Obtener llaves de un usuario específico
   * @param {string} userId - ID del usuario
   * @param {Object} filters - Filtros opcionales
   * @returns {Promise<Object>} Respuesta con llaves y metadatos
   */
  async getKeysByUser(userId, filters = {}) {
    try {
      // Construir query parameters
      const queryParams = this.buildQueryParams(filters);
      const url = `${API_ENDPOINTS.KEYS.BY_USER(userId)}${queryParams}`;

      securityLogger.logInfo('GET_KEYS_BY_USER_REQUEST', {
        userId,
        filters,
        timestamp: new Date().toISOString(),
      });

      const response = await httpClient.get(url);
      
      securityLogger.logInfo('GET_KEYS_BY_USER_SUCCESS', {
        userId,
        totalKeys: response.total,
        successful: response.successful,
        failed: response.failed,
        uploadedToCtm: response.uploadedToCtm,
        timestamp: new Date().toISOString(),
      });

      return response;
    } catch (error) {
      securityLogger.logError('GET_KEYS_BY_USER_ERROR', {
        userId,
        error: error.message,
        status: error.status,
        timestamp: new Date().toISOString(),
      });
      throw error;
    }
  }

  /**
   * Validar datos de llave
   * @param {Object} keyData - Datos de la llave
   */
  validateKeyData(keyData) {
    const errors = [];

    // Validaciones requeridas
    if (!keyData.key_name) {
      errors.push('Nombre de la llave es requerido');
    }

    // Validaciones de formato
    if (keyData.key_name && !this.isValidKeyName(keyData.key_name)) {
      errors.push('Nombre de llave inválido. Solo se permiten letras, números, guiones y guiones bajos');
    }

    if (keyData.algorithm && !this.isValidAlgorithm(keyData.algorithm)) {
      errors.push('Algoritmo inválido. Debe ser AES, RSA o HMAC');
    }

    if (keyData.num_bytes && !this.isValidNumBytes(keyData.num_bytes)) {
      errors.push('Número de bytes inválido. Debe estar entre 1 y 1024');
    }

    if (keyData.exportable !== undefined && typeof keyData.exportable !== 'boolean') {
      errors.push('El campo exportable debe ser un booleano');
    }

    if (keyData.key_material_base64 && !this.isValidBase64(keyData.key_material_base64)) {
      errors.push('Material de llave debe estar en formato Base64 válido');
    }

    if (errors.length > 0) {
      throw new Error(`Datos de llave inválidos: ${errors.join(', ')}`);
    }
  }

  /**
   * Validar nombre de llave
   * @param {string} keyName - Nombre de la llave
   * @returns {boolean} True si es válido
   */
  isValidKeyName(keyName) {
    const keyNameRegex = /^[a-zA-Z0-9_-]+$/;
    return keyNameRegex.test(keyName) && keyName.length >= 1 && keyName.length <= 100;
  }

  /**
   * Validar algoritmo
   * @param {string} algorithm - Algoritmo
   * @returns {boolean} True si es válido
   */
  isValidAlgorithm(algorithm) {
    return ['AES', 'RSA', 'HMAC'].includes(algorithm);
  }

  /**
   * Validar número de bytes
   * @param {number} numBytes - Número de bytes
   * @returns {boolean} True si es válido
   */
  isValidNumBytes(numBytes) {
    return Number.isInteger(numBytes) && numBytes >= 1 && numBytes <= 1024;
  }

  /**
   * Validar formato Base64
   * @param {string} base64String - String en Base64
   * @returns {boolean} True si es válido
   */
  isValidBase64(base64String) {
    try {
      return btoa(atob(base64String)) === base64String;
    } catch {
      return false;
    }
  }

  /**
   * Construir query parameters para filtros
   * @param {Object} filters - Filtros
   * @returns {string} Query string
   */
  buildQueryParams(filters) {
    const params = new URLSearchParams();

    // Paginación
    if (filters.page) {
      params.append('page', filters.page.toString());
    }
    if (filters.limit) {
      params.append('limit', filters.limit.toString());
    }

    // Filtros
    if (filters.type) {
      params.append('type', filters.type);
    }
    if (filters.algorithm) {
      params.append('algorithm', filters.algorithm);
    }
    if (filters.status) {
      params.append('status', filters.status);
    }
    if (filters.uploadedToCtm !== undefined) {
      params.append('uploadedToCtm', filters.uploadedToCtm.toString());
    }
    if (filters.search) {
      params.append('search', filters.search);
    }

    const queryString = params.toString();
    return queryString ? `?${queryString}` : '';
  }

  /**
   * Sanitizar datos de llave
   * @param {Object} keyData - Datos de la llave
   * @returns {Object} Datos sanitizados
   */
  sanitizeKeyData(keyData) {
    const sanitized = {};
    
    for (const [key, value] of Object.entries(keyData)) {
      if (typeof value === 'string') {
        // Para nombres de llave, solo permitir caracteres seguros
        if (key === 'key_name') {
          sanitized[key] = value.replace(/[^a-zA-Z0-9_-]/g, '');
        } else {
          sanitized[key] = value.trim();
        }
      } else {
        sanitized[key] = value;
      }
    }
    
    return sanitized;
  }

  /**
   * Generar datos de llave con valores por defecto
   * @param {Object} keyData - Datos base de la llave
   * @returns {Object} Datos completos de la llave
   */
  prepareKeyData(keyData) {
    const prepared = {
      algorithm: 'AES',
      num_bytes: 32,
      exportable: false,
      ...keyData,
    };

    // Sanitizar datos
    return this.sanitizeKeyData(prepared);
  }

  /**
   * Obtener estadísticas de llaves de un usuario
   * @param {string} userId - ID del usuario
   * @returns {Promise<Object>} Estadísticas de llaves
   */
  async getKeyStatistics(userId) {
    try {
      const response = await this.getKeysByUser(userId, { limit: 1 });
      
      return {
        total: response.total,
        successful: response.successful,
        failed: response.failed,
        uploadedToCtm: response.uploadedToCtm,
        successRate: response.total > 0 ? (response.successful / response.total * 100).toFixed(2) : 0,
        ctmUploadRate: response.total > 0 ? (response.uploadedToCtm / response.total * 100).toFixed(2) : 0,
      };
    } catch (error) {
      securityLogger.logError('GET_KEY_STATISTICS_ERROR', {
        userId,
        error: error.message,
        timestamp: new Date().toISOString(),
      });
      throw error;
    }
  }

  /**
   * Eliminar una llave
   * @param {string} keyId - ID de la llave a eliminar
   * @returns {Promise<Object>} Respuesta de la eliminación
   */
  async deleteKey(keyId) {
    try {
      if (!keyId) {
        throw new Error('ID de llave es requerido');
      }

      securityLogger.logInfo('DELETE_KEY_REQUEST', {
        keyId,
        timestamp: new Date().toISOString(),
      });

      const response = await httpClient.delete(`/keys/${keyId}`);

      securityLogger.logInfo('DELETE_KEY_SUCCESS', {
        keyId,
        timestamp: new Date().toISOString(),
      });

      return response;
    } catch (error) {
      securityLogger.logError('DELETE_KEY_ERROR', {
        keyId,
        error: error.message,
        status: error.status,
        timestamp: new Date().toISOString(),
      });
      throw error;
    }
  }
}

// Exportar instancia singleton
const keyService = new KeyService();
export default keyService;
