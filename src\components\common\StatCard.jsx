import PropTypes from 'prop-types';
import { useRef } from 'react';
import gsap from 'gsap';

/**
 * Componente reutilizable para mostrar tarjetas de estadísticas
 * Versión simplificada sin gráficos ni animaciones de entrada
 */
const StatCard = ({
  icon: Icon,
  title,
  value,
  description,
  iconColor = "text-blue-500",
  valueColor = "text-blue-600 dark:text-blue-400",
  isLoading = false,
  className = ""
}) => {
  const cardRef = useRef(null);

  // Sin animaciones de entrada - solo hover

  // Animaciones de hover
  const handleMouseEnter = () => {
    gsap.to(cardRef.current, {
      y: -8,
      scale: 1.02,
      boxShadow: "0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)",
      duration: 0.3,
      ease: "power2.out"
    });
  };

  const handleMouseLeave = () => {
    gsap.to(cardRef.current, {
      y: 0,
      scale: 1,
      boxShadow: "0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)",
      duration: 0.3,
      ease: "power2.out"
    });
  };

  return (
    <div
      ref={cardRef}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      className={`p-6 rounded-xl border bg-white dark:bg-gray-700 dark:border-gray-600 shadow-lg cursor-pointer ${className}`}
    >
      {/* Header con icono y título */}
      <div className="flex items-center gap-3 mb-4">
        <div className={`p-2 rounded-lg bg-opacity-10 ${iconColor.replace('text-', 'bg-')}`}>
          <Icon size={20} className={iconColor} />
        </div>
        <span className="font-light tracking-wide text-gray-900 dark:text-white">{title}</span>
      </div>

      {/* Valor principal */}
      <p className={`text-3xl font-light tracking-wide leading-relaxed mb-2 ${valueColor}`}>
        {isLoading ? '...' : value}
      </p>

      {/* Descripción */}
      <p className="text-sm font-light tracking-wide text-gray-600 dark:text-gray-300">
        {description}
      </p>
    </div>
  );
};

StatCard.propTypes = {
  icon: PropTypes.elementType.isRequired,
  title: PropTypes.string.isRequired,
  value: PropTypes.oneOfType([PropTypes.string, PropTypes.number]).isRequired,
  description: PropTypes.string.isRequired,
  iconColor: PropTypes.string,
  valueColor: PropTypes.string,
  isLoading: PropTypes.bool,
  className: PropTypes.string
};

export default StatCard;
