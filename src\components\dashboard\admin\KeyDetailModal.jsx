import { Copy, Key, Calendar, Shield, AlertCircle, CheckCircle, Clock } from 'lucide-react';
import PropTypes from 'prop-types';
import { useTranslation } from 'react-i18next';
import { Modal, Button } from '../../common';

/**
 * Modal para mostrar el detalle completo de una llave
 */
const KeyDetailModal = ({
  isOpen,
  onClose,
  keyData,
  darkMode
}) => {
  const { t } = useTranslation();

  if (!keyData) return null;

  const copyToClipboard = (text, label) => {
    navigator.clipboard.writeText(text).then(() => {
      // Aquí podrías agregar una notificación de éxito
      console.log(`${label} copiado al portapapeles`);
    }).catch(() => {
      console.error('Error al copiar al portapapeles');
    });
  };

  // Función para parsear el entropy report
  const parseEntropyReport = (entropyReportString) => {
    try {
      if (!entropyReportString) return null;
      return JSON.parse(entropyReportString);
    } catch (error) {
      console.error('Error parsing entropy report:', error);
      return null;
    }
  };

  const getStatusInfo = () => {
    if (keyData.status === 'UPLOADED_TO_CTM' || keyData.uploadedToCtm === true) {
      return {
        icon: <CheckCircle size={24} className="text-white" />,
        text: t('keys.activeInCTM'),
        color: 'text-green-700 dark:text-green-300',
        bgColor: 'bg-gradient-to-br from-green-50 to-emerald-100 dark:from-green-900/30 dark:to-emerald-900/30',
        borderColor: 'border-green-200 dark:border-green-700',
        iconBg: 'bg-gradient-to-br from-green-500 to-emerald-600'
      };
    }
    if (keyData.status === 'FAILED' || keyData.isSuccessful === false) {
      return {
        icon: <AlertCircle size={24} className="text-white" />,
        text: t('keys.failed'),
        color: 'text-red-700 dark:text-red-300',
        bgColor: 'bg-gradient-to-br from-red-50 to-rose-100 dark:from-red-900/30 dark:to-rose-900/30',
        borderColor: 'border-red-200 dark:border-red-700',
        iconBg: 'bg-gradient-to-br from-red-500 to-rose-600'
      };
    }
    return {
      icon: <Clock size={24} className="text-white" />,
      text: t('keys.pending'),
      color: 'text-yellow-700 dark:text-yellow-300',
      bgColor: 'bg-gradient-to-br from-yellow-50 to-amber-100 dark:from-yellow-900/30 dark:to-amber-900/30',
      borderColor: 'border-yellow-200 dark:border-yellow-700',
      iconBg: 'bg-gradient-to-br from-yellow-500 to-amber-600'
    };
  };

  const statusInfo = getStatusInfo();

  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleString();
  };

  const DetailRow = ({ label, value, copyable = false, isRgbCard = false }) => (
    <div className={`flex justify-between items-start py-4 border-b last:border-b-0 hover:bg-white/5 rounded-lg px-2 transition-all duration-200 ${
      isRgbCard
        ? 'border-white/10'
        : 'border-gray-200/50 dark:border-gray-600/50 hover:bg-white/50 dark:hover:bg-gray-800/30'
    }`}>
      <span className={`font-light tracking-wide min-w-0 flex-shrink-0 w-1/3 ${
        isRgbCard
          ? 'text-white/90'
          : 'text-gray-700 dark:text-gray-300'
      }`}>
        {label}:
      </span>
      <div className="flex items-center gap-3 min-w-0 flex-1">
        <span className={`font-light tracking-wide break-all text-right flex-1 ${
          isRgbCard
            ? 'text-white'
            : 'text-gray-900 dark:text-white'
        }`}>
          {value || 'N/A'}
        </span>
        {copyable && value && (
          <Button
            onClick={() => copyToClipboard(value, label)}
            variant="outline"
            size="sm"
            icon={Copy}
            className="p-2 h-8 w-8 flex-shrink-0 rounded-lg bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 border-gray-300 dark:border-gray-600 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-all duration-200"
            title={`Copiar ${label}`}
          />
        )}
      </div>
    </div>
  );

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={
        <div className="flex items-center gap-4">
          <div className="p-3 rounded-xl bg-gradient-to-br from-blue-500 to-purple-600 shadow-lg">
            <Key size={24} className="text-white" />
          </div>
          <div>
            <h3 className="text-2xl font-light tracking-wide leading-relaxed text-gray-900 dark:text-white">
              {t('modals.keyDetail')}
            </h3>
            <p className="text-sm font-light tracking-wide text-gray-600 dark:text-gray-400">
              {keyData.name}
            </p>
          </div>
        </div>
      }
      maxWidth="max-w-2xl"
      darkMode={darkMode}
    >
      <div className="space-y-6">
        {/* Estado de la llave */}
        <div className={`p-6 rounded-2xl border-2 ${statusInfo.bgColor} ${statusInfo.borderColor} shadow-lg hover:shadow-xl transition-all duration-300`}>
          <div className="flex items-center gap-4">
            <div className={`p-3 rounded-xl ${statusInfo.iconBg} shadow-md`}>
              {statusInfo.icon}
            </div>
            <div className="flex-1">
              <h4 className={`text-lg font-light tracking-wide ${statusInfo.color} mb-1`}>
                {t('keyDetails.status')}: {statusInfo.text}
              </h4>
              <p className="text-sm font-light tracking-wide text-gray-600 dark:text-gray-400 leading-relaxed">
                {keyData.status === 'UPLOADED_TO_CTM'
                  ? t('keyDetails.statusActive')
                  : keyData.status === 'FAILED'
                  ? t('keyDetails.statusFailed')
                  : t('keyDetails.statusPending')
                }
              </p>
            </div>
          </div>
        </div>

        {/* Información básica */}
        <div className={`p-6 rounded-2xl border-2 shadow-lg hover:shadow-xl transition-all duration-300 ${
          darkMode ? 'bg-gradient-to-br from-gray-800 to-gray-700 border-gray-600' : 'bg-gradient-to-br from-white to-gray-50 border-gray-200'
        }`}>
          <h4 className="text-lg font-light tracking-wide mb-4 flex items-center gap-3 text-gray-900 dark:text-white">
            <div className="p-2 rounded-xl bg-gradient-to-br from-blue-500 to-indigo-600 shadow-md">
              <Shield size={20} className="text-white" />
            </div>
            {t('modals.basicInformation')}
          </h4>
          <div className="space-y-0">
            <DetailRow label={t('keyDetails.name')} value={keyData.name} />
            <DetailRow
              label={keyData.ctmKeyId ? t('keyDetails.ctmKeyId') : t('keyDetails.localId')}
              value={keyData.ctmKeyId || keyData.id}
            />
            {keyData.ctmKeyId && (
              <DetailRow label={t('keyDetails.localId')} value={keyData.id} />
            )}
            <DetailRow label={t('keyDetails.type')} value={keyData.type || keyData.algorithm} />
            <DetailRow label={t('keyDetails.numBytes')} value={keyData.numBytes || keyData.num_bytes || 'N/A'} />
            <DetailRow label={t('keyDetails.exportable')} value={keyData.exportable ? t('keyDetails.yes') : t('keyDetails.no')} />
          </div>
        </div>

        {/* Entropy Report - RGB Style */}
        {keyData.entropyReport && (
          <div className="relative p-6 rounded-2xl border shadow-2xl hover:shadow-3xl transition-all duration-500 overflow-hidden"
          style={{
            background: darkMode
              ? 'linear-gradient(135deg, #000000 0%, #1a1a1a 25%, #ff0066 50%, #00ffff 75%, #000000 100%)'
              : 'linear-gradient(135deg, #000000 0%, #ff0066 25%, #00ffff 50%, #ff6600 75%, #000000 100%)',
            backgroundSize: '400% 400%',
            animation: 'rgbGlow 4s ease-in-out infinite',
            borderColor: 'rgba(255, 255, 255, 0.1)'
          }}>
            {/* Overlay sutil para legibilidad */}
            <div className="absolute inset-0 bg-black/30 rounded-2xl"></div>

            {/* Contenido */}
            <div className="relative z-10">
              <h4 className="text-lg font-light tracking-wide mb-4 flex items-center gap-3 text-white">
                <div className="p-2 rounded-xl bg-gradient-to-br from-cyan-400 via-purple-500 to-pink-500 shadow-lg">
                  <Shield size={20} className="text-white" />
                </div>
                {t('keyDetails.entropyReport')}
              </h4>
            <div className="space-y-0">
              {(() => {
                const entropyData = parseEntropyReport(keyData.entropyReport);
                if (!entropyData) {
                  return <DetailRow label="Datos" value="Error al parsear el reporte" />;
                }
                return (
                  <>
                    {entropyData.entropy_status && (
                      <DetailRow label={t('keyDetails.entropyStatus')} value={entropyData.entropy_status} isRgbCard={true} />
                    )}
                    {/* Cadena de Entropía omitida intencionalmente */}
                    {entropyData.quantum_fidelity && (
                      <DetailRow label={t('keyDetails.quantumFidelity')} value={entropyData.quantum_fidelity} isRgbCard={true} />
                    )}
                    {entropyData.source && (
                      <DetailRow label={t('keyDetails.source')} value={entropyData.source} isRgbCard={true} />
                    )}
                    {entropyData.error_string && (
                      <DetailRow label={t('keyDetails.error')} value={entropyData.error_string} isRgbCard={true} />
                    )}
                  </>
                );
              })()}
            </div>
            </div>
          </div>
        )}

        {/* Fechas */}
        <div className={`p-6 rounded-2xl border-2 shadow-lg hover:shadow-xl transition-all duration-300 ${
          darkMode ? 'bg-gradient-to-br from-emerald-900/20 to-teal-900/20 border-emerald-700' : 'bg-gradient-to-br from-emerald-50 to-teal-100 border-emerald-200'
        }`}>
          <h4 className="text-lg font-light tracking-wide mb-4 flex items-center gap-3 text-gray-900 dark:text-white">
            <div className="p-2 rounded-xl bg-gradient-to-br from-emerald-500 to-teal-600 shadow-md">
              <Calendar size={20} className="text-white" />
            </div>
            {t('keyDetails.dates')}
          </h4>
          <div className="space-y-0">
            <DetailRow label={t('keyDetails.created')} value={formatDate(keyData.createdAt)} />
            <DetailRow label={t('keyDetails.updated')} value={formatDate(keyData.updatedAt)} />
            {keyData.uploadedAt && (
              <DetailRow label={t('keyDetails.uploaded')} value={formatDate(keyData.uploadedAt)} />
            )}
          </div>
        </div>

        {/* Material de la llave (si está disponible) */}
        {keyData.key_material_base64 && (
          <div className={`p-4 rounded-lg border ${
            darkMode ? 'bg-gray-700 border-gray-600' : 'bg-gray-50 border-gray-200'
          }`}>
            <h4 className="font-semibold mb-3 flex items-center gap-2">
              <Key size={18} />
              {t('keyDetails.keyMaterial')}
            </h4>
            <div className="space-y-0">
              <DetailRow 
                label="Base64" 
                value={keyData.key_material_base64.substring(0, 50) + '...'} 
                copyable 
              />
            </div>
            <p className="text-xs text-gray-500 mt-2">
              ⚠️ Esta información es sensible y debe manejarse con cuidado
            </p>
          </div>
        )}

        {/* Información técnica adicional */}
        {keyData.error_message && (
          <div className={`p-4 rounded-lg border ${
            darkMode ? 'bg-gray-700 border-gray-600' : 'bg-gray-50 border-gray-200'
          }`}>
            <h4 className="font-semibold mb-3">Información Técnica</h4>
            <div className="space-y-0">
              <DetailRow label="Mensaje de Error" value={keyData.error_message} />
            </div>
          </div>
        )}
      </div>

      {/* Footer con botón centrado elegante */}
      <div className="flex justify-center mt-8 pt-6 border-t-2 border-gray-200 dark:border-gray-600 bg-gray-50/30 dark:bg-gray-700/20 -mx-8 px-8 pb-6 rounded-b-2xl">
        <button
          onClick={onClose}
          className="px-4 py-2 rounded-xl font-light tracking-wide border-2 border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 hover:border-gray-400 dark:hover:border-gray-500 transform hover:scale-105 transition-all duration-300 shadow-lg hover:shadow-xl"
        >
          {t('modals.close')}
        </button>
      </div>
    </Modal>
  );
};

KeyDetailModal.propTypes = {
  isOpen: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  keyData: PropTypes.object,
  darkMode: PropTypes.bool.isRequired
};

export default KeyDetailModal;
