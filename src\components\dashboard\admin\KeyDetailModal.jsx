import { Copy, Key, Calendar, Shield, AlertCircle, CheckCircle, Clock } from 'lucide-react';
import PropTypes from 'prop-types';
import { Modal, Button } from '../../common';

/**
 * Modal para mostrar el detalle completo de una llave
 */
const KeyDetailModal = ({ 
  isOpen, 
  onClose, 
  keyData, 
  darkMode 
}) => {
  if (!keyData) return null;

  const copyToClipboard = (text, label) => {
    navigator.clipboard.writeText(text).then(() => {
      // Aquí podrías agregar una notificación de éxito
      console.log(`${label} copiado al portapapeles`);
    }).catch(() => {
      console.error('Error al copiar al portapapeles');
    });
  };

  // Función para parsear el entropy report
  const parseEntropyReport = (entropyReportString) => {
    try {
      if (!entropyReportString) return null;
      return JSON.parse(entropyReportString);
    } catch (error) {
      console.error('Error parsing entropy report:', error);
      return null;
    }
  };

  const getStatusInfo = () => {
    if (keyData.status === 'UPLOADED_TO_CTM' || keyData.uploadedToCtm === true) {
      return {
        icon: <CheckCircle size={24} className="text-white" />,
        text: 'Activa en CTM',
        color: 'text-green-700 dark:text-green-300',
        bgColor: 'bg-gradient-to-br from-green-50 to-emerald-100 dark:from-green-900/30 dark:to-emerald-900/30',
        borderColor: 'border-green-200 dark:border-green-700',
        iconBg: 'bg-gradient-to-br from-green-500 to-emerald-600'
      };
    }
    if (keyData.status === 'FAILED' || keyData.isSuccessful === false) {
      return {
        icon: <AlertCircle size={24} className="text-white" />,
        text: 'Fallida',
        color: 'text-red-700 dark:text-red-300',
        bgColor: 'bg-gradient-to-br from-red-50 to-rose-100 dark:from-red-900/30 dark:to-rose-900/30',
        borderColor: 'border-red-200 dark:border-red-700',
        iconBg: 'bg-gradient-to-br from-red-500 to-rose-600'
      };
    }
    return {
      icon: <Clock size={24} className="text-white" />,
      text: 'Pendiente',
      color: 'text-yellow-700 dark:text-yellow-300',
      bgColor: 'bg-gradient-to-br from-yellow-50 to-amber-100 dark:from-yellow-900/30 dark:to-amber-900/30',
      borderColor: 'border-yellow-200 dark:border-yellow-700',
      iconBg: 'bg-gradient-to-br from-yellow-500 to-amber-600'
    };
  };

  const statusInfo = getStatusInfo();

  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleString();
  };

  const DetailRow = ({ label, value, copyable = false }) => (
    <div className="flex justify-between items-start py-4 border-b border-gray-200/50 dark:border-gray-600/50 last:border-b-0 hover:bg-white/50 dark:hover:bg-gray-800/30 rounded-lg px-2 transition-all duration-200">
      <span className="font-light tracking-wide text-gray-700 dark:text-gray-300 min-w-0 flex-shrink-0 w-1/3">
        {label}:
      </span>
      <div className="flex items-center gap-3 min-w-0 flex-1">
        <span className="font-light tracking-wide text-gray-900 dark:text-white break-all text-right flex-1">
          {value || 'N/A'}
        </span>
        {copyable && value && (
          <Button
            onClick={() => copyToClipboard(value, label)}
            variant="outline"
            size="sm"
            icon={Copy}
            className="p-2 h-8 w-8 flex-shrink-0 rounded-lg bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 border-gray-300 dark:border-gray-600 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-all duration-200"
            title={`Copiar ${label}`}
          />
        )}
      </div>
    </div>
  );

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={
        <div className="flex items-center gap-4">
          <div className="p-3 rounded-xl bg-gradient-to-br from-blue-500 to-purple-600 shadow-lg">
            <Key size={24} className="text-white" />
          </div>
          <div>
            <h3 className="text-2xl font-light tracking-wide leading-relaxed text-gray-900 dark:text-white">
              Detalle de Llave
            </h3>
            <p className="text-sm font-light tracking-wide text-gray-600 dark:text-gray-400">
              {keyData.name}
            </p>
          </div>
        </div>
      }
      maxWidth="max-w-2xl"
      darkMode={darkMode}
    >
      <div className="space-y-6">
        {/* Estado de la llave */}
        <div className={`p-6 rounded-2xl border-2 ${statusInfo.bgColor} ${statusInfo.borderColor} shadow-lg hover:shadow-xl transition-all duration-300`}>
          <div className="flex items-center gap-4">
            <div className={`p-3 rounded-xl ${statusInfo.iconBg} shadow-md`}>
              {statusInfo.icon}
            </div>
            <div className="flex-1">
              <h4 className={`text-lg font-light tracking-wide ${statusInfo.color} mb-1`}>
                Estado: {statusInfo.text}
              </h4>
              <p className="text-sm font-light tracking-wide text-gray-600 dark:text-gray-400 leading-relaxed">
                {keyData.status === 'UPLOADED_TO_CTM'
                  ? 'La llave está activa y funcionando perfectamente en CipherTrust Manager'
                  : keyData.status === 'FAILED'
                  ? 'Hubo un error durante el procesamiento de esta llave'
                  : 'La llave está siendo procesada y validada en el sistema'
                }
              </p>
            </div>
          </div>
        </div>

        {/* Información básica */}
        <div className={`p-6 rounded-2xl border-2 shadow-lg hover:shadow-xl transition-all duration-300 ${
          darkMode ? 'bg-gradient-to-br from-gray-800 to-gray-700 border-gray-600' : 'bg-gradient-to-br from-white to-gray-50 border-gray-200'
        }`}>
          <h4 className="text-lg font-light tracking-wide mb-4 flex items-center gap-3 text-gray-900 dark:text-white">
            <div className="p-2 rounded-xl bg-gradient-to-br from-blue-500 to-indigo-600 shadow-md">
              <Shield size={20} className="text-white" />
            </div>
            Información Básica
          </h4>
          <div className="space-y-0">
            <DetailRow label="Nombre" value={keyData.name} />
            <DetailRow
              label={keyData.ctmKeyId ? "CTM Key ID" : "ID Local"}
              value={keyData.ctmKeyId || keyData.id}
            />
            {keyData.ctmKeyId && (
              <DetailRow label="ID Local" value={keyData.id} />
            )}
            <DetailRow label="Tipo/Algoritmo" value={keyData.type || keyData.algorithm} />
            <DetailRow label="Número de Bytes" value={keyData.numBytes || keyData.num_bytes || 'N/A'} />
            <DetailRow label="Exportable" value={keyData.exportable ? 'Sí' : 'No'} />
          </div>
        </div>

        {/* Entropy Report - RGB Style */}
        {keyData.entropyReport && (
          <div className={`relative p-6 rounded-2xl border-2 shadow-2xl hover:shadow-3xl transition-all duration-500 overflow-hidden ${
            darkMode
              ? 'bg-gradient-to-br from-black via-gray-900 to-black border-transparent'
              : 'bg-gradient-to-br from-gray-900 via-black to-gray-900 border-transparent'
          }`}
          style={{
            background: darkMode
              ? 'linear-gradient(135deg, #000000 0%, #1a1a1a 25%, #ff0066 50%, #00ffff 75%, #000000 100%)'
              : 'linear-gradient(135deg, #000000 0%, #ff0066 25%, #00ffff 50%, #ff6600 75%, #000000 100%)',
            backgroundSize: '400% 400%',
            animation: 'rgbGlow 4s ease-in-out infinite'
          }}>
            {/* Overlay para mejor legibilidad */}
            <div className="absolute inset-0 bg-black/60 rounded-2xl"></div>

            {/* Contenido */}
            <div className="relative z-10">
              <h4 className="text-lg font-light tracking-wide mb-4 flex items-center gap-3 text-white">
                <div className="p-2 rounded-xl bg-gradient-to-br from-cyan-400 via-purple-500 to-pink-500 shadow-lg animate-pulse">
                  <Shield size={20} className="text-white" />
                </div>
                Reporte de Auto-certificación
              </h4>
            <div className="space-y-0">
              {(() => {
                const entropyData = parseEntropyReport(keyData.entropyReport);
                if (!entropyData) {
                  return <DetailRow label="Datos" value="Error al parsear el reporte" />;
                }
                return (
                  <>
                    {entropyData.entropy_status && (
                      <DetailRow label="Estado de Entropía" value={entropyData.entropy_status} />
                    )}
                    {/* Cadena de Entropía omitida intencionalmente */}
                    {entropyData.quantum_fidelity && (
                      <DetailRow label="Fidelidad Cuántica" value={entropyData.quantum_fidelity} />
                    )}
                    {entropyData.source && (
                      <DetailRow label="Fuente" value={entropyData.source} />
                    )}
                    {entropyData.error_string && (
                      <DetailRow label="Error" value={entropyData.error_string} />
                    )}
                  </>
                );
              })()}
            </div>
            </div>
          </div>
        )}

        {/* Fechas */}
        <div className={`p-6 rounded-2xl border-2 shadow-lg hover:shadow-xl transition-all duration-300 ${
          darkMode ? 'bg-gradient-to-br from-emerald-900/20 to-teal-900/20 border-emerald-700' : 'bg-gradient-to-br from-emerald-50 to-teal-100 border-emerald-200'
        }`}>
          <h4 className="text-lg font-light tracking-wide mb-4 flex items-center gap-3 text-gray-900 dark:text-white">
            <div className="p-2 rounded-xl bg-gradient-to-br from-emerald-500 to-teal-600 shadow-md">
              <Calendar size={20} className="text-white" />
            </div>
            Fechas
          </h4>
          <div className="space-y-0">
            <DetailRow label="Creada" value={formatDate(keyData.createdAt)} />
            <DetailRow label="Actualizada" value={formatDate(keyData.updatedAt)} />
            {keyData.uploadedAt && (
              <DetailRow label="Subida a CTM" value={formatDate(keyData.uploadedAt)} />
            )}
          </div>
        </div>

        {/* Material de la llave (si está disponible) */}
        {keyData.key_material_base64 && (
          <div className={`p-4 rounded-lg border ${
            darkMode ? 'bg-gray-700 border-gray-600' : 'bg-gray-50 border-gray-200'
          }`}>
            <h4 className="font-semibold mb-3 flex items-center gap-2">
              <Key size={18} />
              Material de la Llave
            </h4>
            <div className="space-y-0">
              <DetailRow 
                label="Base64" 
                value={keyData.key_material_base64.substring(0, 50) + '...'} 
                copyable 
              />
            </div>
            <p className="text-xs text-gray-500 mt-2">
              ⚠️ Esta información es sensible y debe manejarse con cuidado
            </p>
          </div>
        )}

        {/* Información técnica adicional */}
        {keyData.error_message && (
          <div className={`p-4 rounded-lg border ${
            darkMode ? 'bg-gray-700 border-gray-600' : 'bg-gray-50 border-gray-200'
          }`}>
            <h4 className="font-semibold mb-3">Información Técnica</h4>
            <div className="space-y-0">
              <DetailRow label="Mensaje de Error" value={keyData.error_message} />
            </div>
          </div>
        )}
      </div>

      {/* Footer con botón centrado elegante */}
      <div className="flex justify-center mt-8 pt-6 border-t-2 border-gray-200 dark:border-gray-600 bg-gray-50/30 dark:bg-gray-700/20 -mx-8 px-8 pb-6 rounded-b-2xl">
        <button
          onClick={onClose}
          className="px-4 py-2 rounded-xl font-light tracking-wide border-2 border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 hover:border-gray-400 dark:hover:border-gray-500 transform hover:scale-105 transition-all duration-300 shadow-lg hover:shadow-xl"
        >
          Cerrar
        </button>
      </div>
    </Modal>
  );
};

KeyDetailModal.propTypes = {
  isOpen: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  keyData: PropTypes.object,
  darkMode: PropTypes.bool.isRequired
};

export default KeyDetailModal;
