import PropTypes from 'prop-types';
import { cloneElement, Children } from 'react';
import { Sidebar, MainContent } from '../common';
import { useDarkMode, useSecurity } from '../../hooks';

/**
 * Componente layout base para dashboards
 * Proporciona estructura común con sidebar y contenido principal
 */
const DashboardLayout = ({
  title,
  currentUser,
  navigationItems,
  activeSection,
  onSectionChange,
  onLogout,
  expectedRole,
  children
}) => {
  const { darkMode, toggleDarkMode } = useDarkMode();

  // Configurar monitoreo de seguridad
  useSecurity(currentUser, expectedRole, onLogout);

  // Clonar children y pasar darkMode como prop
  const childrenWithProps = Children.map(children, child => {
    if (child && typeof child === 'object' && child.props) {
      return cloneElement(child, { darkMode });
    }
    return child;
  });

  return (
    <div
      className={`flex h-screen transition-colors duration-500 ${
        darkMode ? "bg-gray-900 text-white" : "bg-gray-200 text-gray-900"
      }`}
    >
      <Sidebar
        title={title}
        currentUser={currentUser}
        darkMode={darkMode}
        onToggleDarkMode={toggleDarkMode}
        onLogout={onLogout}
        navigationItems={navigationItems}
        activeSection={activeSection}
        onSectionChange={onSectionChange}
      />

      <MainContent darkMode={darkMode}>
        {childrenWithProps}
      </MainContent>
    </div>
  );
};

DashboardLayout.propTypes = {
  title: PropTypes.string.isRequired,
  currentUser: PropTypes.object,
  navigationItems: PropTypes.arrayOf(PropTypes.shape({
    key: PropTypes.string.isRequired,
    label: PropTypes.string.isRequired,
    icon: PropTypes.elementType.isRequired
  })).isRequired,
  activeSection: PropTypes.string.isRequired,
  onSectionChange: PropTypes.func.isRequired,
  onLogout: PropTypes.func.isRequired,
  expectedRole: PropTypes.oneOfType([
    PropTypes.string,
    PropTypes.arrayOf(PropTypes.string)
  ]).isRequired,
  children: PropTypes.node.isRequired
};

export default DashboardLayout;
