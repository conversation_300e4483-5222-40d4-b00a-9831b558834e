import { Edit, Trash2, Key } from 'lucide-react';
import PropTypes from 'prop-types';
import { Button } from '../../common';

/**
 * Componente para mostrar información de un usuario en formato de tarjeta con hover animations
 */
const UserCard = ({ user, darkMode, onEdit, onDelete, onViewKeys }) => {
  return (
    <div
      className={`p-4 rounded-lg border transition-all duration-300 cursor-pointer ${
        darkMode
          ? 'bg-gray-700 border-gray-600 hover:bg-gray-600'
          : 'bg-white border-gray-200 hover:bg-gray-50'
      }`}
    >
      <div className="flex justify-between items-start">
        <div className="flex-1">
          <div className="flex items-center gap-3 mb-2">
            <h3 className="text-lg font-light tracking-wide">
              {user.firstName} {user.lastName}
            </h3>
            {/* Badge de estado compacto */}
            <div className={`flex items-center gap-1.5 px-2 py-1 rounded-lg text-xs ${
              user.isActive
                ? 'bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-300'
                : 'bg-red-100 text-red-700 dark:bg-red-900/30 dark:text-red-300'
            }`}>
              <div className={`w-1.5 h-1.5 rounded-full ${
                user.isActive ? 'bg-green-500' : 'bg-red-500'
              }`}></div>
              <span className="font-light">
                {user.isActive ? 'Activo' : 'Inactivo'}
              </span>
            </div>

            {/* Badge de rol compacto */}
            <div className={`flex items-center gap-1.5 px-2 py-1 rounded-lg text-xs ${
              user.role === 'ADMIN'
                ? 'bg-purple-100 text-purple-700 dark:bg-purple-900/30 dark:text-purple-300'
                : 'bg-blue-100 text-blue-700 dark:bg-blue-900/30 dark:text-blue-300'
            }`}>
              <div className={`w-1.5 h-1.5 rounded-full ${
                user.role === 'ADMIN' ? 'bg-purple-500' : 'bg-blue-500'
              }`}></div>
              <span className="font-light">
                {user.role === 'ADMIN' ? 'Admin' : 'Usuario'}
              </span>
            </div>
          </div>

          <p className="text-sm text-gray-600 dark:text-gray-300 mb-2 font-light">{user.email}</p>
          
          {user.company && (
            <p className="text-sm text-gray-500 dark:text-gray-400 mb-4">
              Empresa: {user.company}
            </p>
          )}

          {/* Información de configuración */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
            {user.ctmIpAddress && (
              <div className={`p-3 rounded border ${
                darkMode ? 'bg-gray-600 border-gray-500' : 'bg-gray-50 border-gray-200'
              }`}>
                <h5 className="font-medium text-sm mb-1">CTM Configuration</h5>
                <p className="text-xs text-gray-600 dark:text-gray-300">
                  IP: {user.ctmIpAddress}
                </p>
                <p className="text-xs text-gray-600 dark:text-gray-300">
                  Usuario: {user.ctmUsername}
                </p>
                <p className="text-xs text-gray-600 dark:text-gray-300">
                  Dominio: {user.ctmDomain}
                </p>
              </div>
            )}

            {user.seqrngIpAddress && (
              <div className={`p-3 rounded border ${
                darkMode ? 'bg-gray-600 border-gray-500' : 'bg-gray-50 border-gray-200'
              }`}>
                <h5 className="font-medium text-sm mb-1">SEQRNG Configuration</h5>
                <p className="text-xs text-gray-600 dark:text-gray-300">
                  IP: {user.seqrngIpAddress}
                </p>
              </div>
            )}
          </div>

          {/* Información de llaves del usuario */}
          {user.keyStats && (
            <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-600">
              <h5 className="font-light tracking-wide text-sm mb-3 text-gray-900 dark:text-white">
                Estadísticas de Llaves
              </h5>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                <div className="text-center p-3 rounded-xl bg-blue-50 dark:bg-blue-900/20 hover:bg-blue-100 dark:hover:bg-blue-900/30 transition-all duration-300">
                  <p className="text-lg font-light tracking-wide text-blue-600 dark:text-blue-400">
                    {user.keyStats.total}
                  </p>
                  <p className="text-xs font-light tracking-wide text-gray-600 dark:text-gray-300">Total</p>
                </div>
                <div className="text-center p-3 rounded-xl bg-green-50 dark:bg-green-900/20 hover:bg-green-100 dark:hover:bg-green-900/30 transition-all duration-300">
                  <p className="text-lg font-light tracking-wide text-green-600 dark:text-green-400">
                    {user.keyStats.successful}
                  </p>
                  <p className="text-xs font-light tracking-wide text-gray-600 dark:text-gray-300">Exitosas</p>
                </div>
                <div className="text-center p-3 rounded-xl bg-red-50 dark:bg-red-900/20 hover:bg-red-100 dark:hover:bg-red-900/30 transition-all duration-300">
                  <p className="text-lg font-light tracking-wide text-red-600 dark:text-red-400">
                    {user.keyStats.failed}
                  </p>
                  <p className="text-xs font-light tracking-wide text-gray-600 dark:text-gray-300">Fallidas</p>
                </div>
                <div className="text-center p-3 rounded-xl bg-purple-50 dark:bg-purple-900/20 hover:bg-purple-100 dark:hover:bg-purple-900/30 transition-all duration-300">
                  <p className="text-lg font-light tracking-wide text-purple-600 dark:text-purple-400">
                    {user.keyStats.uploadedToCtm}
                  </p>
                  <p className="text-xs font-light tracking-wide text-gray-600 dark:text-gray-300">En CTM</p>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Botones de acción compactos */}
        <div className="flex gap-1 ml-3">
          <button
            onClick={() => onViewKeys(user)}
            className="p-2 rounded-lg bg-green-100 hover:bg-green-200 text-green-700 dark:bg-green-900/50 dark:hover:bg-green-800 dark:text-green-300 transition-colors duration-200"
            title="Ver llaves del usuario"
          >
            <Key size={14} />
          </button>
          <button
            onClick={onEdit}
            className="p-2 rounded-lg bg-blue-100 hover:bg-blue-200 text-blue-700 dark:bg-blue-900/50 dark:hover:bg-blue-800 dark:text-blue-300 transition-colors duration-200"
            title="Editar usuario"
          >
            <Edit size={14} />
          </button>
          <button
            onClick={onDelete}
            className="p-2 rounded-lg bg-red-100 hover:bg-red-200 text-red-700 dark:bg-red-900/50 dark:hover:bg-red-800 dark:text-red-300 transition-colors duration-200"
            title="Eliminar usuario"
          >
            <Trash2 size={14} />
          </button>
        </div>
      </div>
    </div>
  );
};

UserCard.propTypes = {
  user: PropTypes.object.isRequired,
  darkMode: PropTypes.bool.isRequired,
  onEdit: PropTypes.func.isRequired,
  onDelete: PropTypes.func.isRequired,
  onViewKeys: PropTypes.func.isRequired
};

export default UserCard;
