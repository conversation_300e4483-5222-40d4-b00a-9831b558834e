import { useState } from 'react';

/**
 * Hook personalizado para manejar el modo oscuro
 * Centraliza la lógica de dark mode para reutilización
 */
const useDarkMode = (initialValue = false) => {
  const [darkMode, setDarkMode] = useState(initialValue);

  const toggleDarkMode = () => {
    const newDarkMode = !darkMode;
    setDarkMode(newDarkMode);

    // Agregar/quitar clase 'dark' del HTML para que Tailwind funcione
    if (newDarkMode) {
      document.documentElement.classList.add('dark');
    } else {
      document.documentElement.classList.remove('dark');
    }
  };

  return {
    darkMode,
    toggleDarkMode,
    setDarkMode
  };
};

export default useDarkMode;
