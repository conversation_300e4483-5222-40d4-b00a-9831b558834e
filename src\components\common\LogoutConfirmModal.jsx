import { useRef, useEffect } from 'react';
import { LogOut, X } from 'lucide-react';
import gsap from 'gsap';
import PropTypes from 'prop-types';
import { useLanguage } from '../../i18n';
import Button from './Button';

/**
 * Modal de confirmación para cerrar sesión
 */
const LogoutConfirmModal = ({ isOpen, onConfirm, onCancel, darkMode }) => {
  const { t } = useLanguage();
  const modalRef = useRef(null);
  const overlayRef = useRef(null);

  // Animaciones GSAP
  useEffect(() => {
    if (isOpen && modalRef.current && overlayRef.current) {
      // Bloquear scroll del body
      document.body.style.overflow = 'hidden';
      
      // Animación de entrada
      gsap.set(modalRef.current, { scale: 0.8, opacity: 0 });
      gsap.set(overlayRef.current, { opacity: 0 });
      
      gsap.to(overlayRef.current, { opacity: 1, duration: 0.3 });
      gsap.to(modalRef.current, { 
        scale: 1, 
        opacity: 1, 
        duration: 0.4, 
        ease: "back.out(1.7)" 
      });
    }

    return () => {
      // Restaurar scroll del body
      document.body.style.overflow = 'unset';
    };
  }, [isOpen]);

  const handleClose = () => {
    if (modalRef.current && overlayRef.current) {
      gsap.to(modalRef.current, { 
        scale: 0.8, 
        opacity: 0, 
        duration: 0.3 
      });
      gsap.to(overlayRef.current, { 
        opacity: 0, 
        duration: 0.3,
        onComplete: onCancel
      });
    } else {
      onCancel();
    }
  };

  const handleConfirm = () => {
    if (modalRef.current && overlayRef.current) {
      gsap.to(modalRef.current, { 
        scale: 0.8, 
        opacity: 0, 
        duration: 0.3 
      });
      gsap.to(overlayRef.current, { 
        opacity: 0, 
        duration: 0.3,
        onComplete: onConfirm
      });
    } else {
      onConfirm();
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
      {/* Overlay */}
      <div 
        ref={overlayRef}
        className="absolute inset-0 bg-black bg-opacity-50"
        onClick={handleClose}
      />
      
      {/* Modal */}
      <div
        ref={modalRef}
        className={`relative w-full max-w-md p-6 rounded-2xl shadow-2xl border-2 ${
          darkMode 
            ? 'bg-gray-800 border-gray-600 text-white' 
            : 'bg-white border-gray-200 text-gray-900'
        }`}
      >
        {/* Botón cerrar */}
        <button
          onClick={handleClose}
          className={`absolute top-4 right-4 p-2 rounded-xl transition-all duration-200 ${
            darkMode 
              ? 'hover:bg-gray-700 text-gray-400 hover:text-white' 
              : 'hover:bg-gray-100 text-gray-500 hover:text-gray-700'
          }`}
        >
          <X size={20} />
        </button>

        {/* Icono principal */}
        <div className="flex justify-center mb-6">
          <div className="w-16 h-16 rounded-full bg-gradient-to-br from-red-500 to-rose-600 flex items-center justify-center shadow-lg">
            <LogOut size={28} className="text-white" />
          </div>
        </div>

        {/* Título */}
        <h2 className="text-xl font-medium text-center mb-4">
          {t('logout.confirmTitle')}
        </h2>

        {/* Mensaje */}
        <p className={`text-center mb-6 ${
          darkMode ? 'text-gray-300' : 'text-gray-600'
        }`}>
          {t('logout.confirmMessage')}
        </p>

        {/* Botones */}
        <div className="flex gap-3">
          <button
            onClick={handleClose}
            className={`flex-1 px-4 py-2 rounded-lg transition-all duration-200 border ${
              darkMode
                ? 'border-gray-600 text-gray-300 hover:bg-gray-700'
                : 'border-gray-300 text-gray-700 hover:bg-gray-50'
            }`}
          >
            {t('logout.cancel')}
          </button>

          <button
            onClick={handleConfirm}
            className="flex-1 px-4 py-2 rounded-lg text-white bg-red-600 hover:bg-red-700 transition-all duration-200"
          >
            {t('logout.confirm')}
          </button>
        </div>
      </div>
    </div>
  );
};

LogoutConfirmModal.propTypes = {
  isOpen: PropTypes.bool.isRequired,
  onConfirm: PropTypes.func.isRequired,
  onCancel: PropTypes.func.isRequired,
  darkMode: PropTypes.bool.isRequired
};

export default LogoutConfirmModal;
