import { useState } from 'react';
import { Plus, Edit, Trash2 } from 'lucide-react';
import PropTypes from 'prop-types';
import { <PERSON><PERSON>, <PERSON>ading<PERSON><PERSON><PERSON>, <PERSON>rror<PERSON><PERSON>t } from '../../common';
import UserCard from './UserCard';
import UserEditModal from './UserEditModal';
import UserCreateModal from './UserCreateModal';
import UserDeleteModal from './UserDeleteModal';
import UserKeysModal from './UserKeysModal';

/**
 * Componente para gestión de usuarios en el AdminDashboard
 */
const UserManagement = ({
  users,
  usersWithKeys,
  isLoading,
  error,
  onClearError,
  onUpdateUser,
  onCreateUser,
  onDeleteUser,
  onGetUserKeys,
  darkMode
}) => {
  const [showEditModal, setShowEditModal] = useState(false);
  const [editingUser, setEditingUser] = useState(null);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [userToDelete, setUserToDelete] = useState(null);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showKeysModal, setShowKeysModal] = useState(false);
  const [selectedUser, setSelectedUser] = useState(null);
  const [userKeys, setUserKeys] = useState([]);
  const [keysLoading, setKeysLoading] = useState(false);

  const displayUsers = usersWithKeys.length > 0 ? usersWithKeys : users;

  const handleEditUser = (user) => {
    setEditingUser({ ...user });
    setShowEditModal(true);
  };

  const handleDeleteUser = (user) => {
    setUserToDelete(user);
    setShowDeleteModal(true);
  };

  const handleSaveUser = async (userData) => {
    await onUpdateUser(editingUser.id, userData);
    setShowEditModal(false);
    setEditingUser(null);
  };

  const handleCreateUser = async (userData) => {
    await onCreateUser(userData);
    setShowCreateModal(false);
  };

  const handleConfirmDelete = async () => {
    try {
      await onDeleteUser(userToDelete.id);
      setShowDeleteModal(false);
      setUserToDelete(null);
    } catch (error) {
      console.error('Error eliminando usuario:', error);
      // TODO: Mostrar notificación de error al usuario
    }
  };

  const handleViewKeys = async (user) => {
    setSelectedUser(user);
    setShowKeysModal(true);
    setKeysLoading(true);

    try {
      if (onGetUserKeys) {
        const keys = await onGetUserKeys(user.id);
        setUserKeys(keys || []);
      } else {
        // Si no hay función específica, usar las llaves del usuario si están disponibles
        setUserKeys(user.keys || []);
      }
    } catch (error) {
      console.error('Error loading user keys:', error);
      setUserKeys([]);
    } finally {
      setKeysLoading(false);
    }
  };

  const handleCloseKeysModal = () => {
    setShowKeysModal(false);
    setSelectedUser(null);
    setUserKeys([]);
    setKeysLoading(false);
  };

  return (
    <>
      {/* Título elegante */}
      <div className="mb-6">
        <h1 className="text-3xl font-light tracking-wide leading-relaxed mb-2">Gestión de Usuarios</h1>
        <p className="text-gray-600 dark:text-white font-light tracking-wide">
          Administra los usuarios del sistema QRNG Quantum
        </p>
      </div>

      {/* Botón Ingresar Usuario centrado */}
      <div className="flex justify-center mb-8">
        <button
          onClick={() => setShowCreateModal(true)}
          className="flex items-center justify-center gap-3 py-3 px-8 rounded-xl font-light tracking-wide shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 group bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white"
        >
          <div className="p-1 bg-white/20 rounded-lg group-hover:bg-white/30 transition-all duration-300">
            <Plus size={16} className="group-hover:rotate-90 transition-transform duration-300" />
          </div>
          <span className="font-light tracking-wide">
            Ingresar Usuario
          </span>
        </button>
      </div>

      <ErrorAlert error={error} onClose={onClearError} />

      {isLoading ? (
        <LoadingSpinner message="Cargando usuarios y estadísticas..." />
      ) : (
        <div className="max-h-96 overflow-y-auto space-y-3 pr-4">
          {displayUsers.length === 0 ? (
            <div className="text-center py-8 text-gray-500 dark:text-gray-400 font-light tracking-wide">
              No hay usuarios registrados
            </div>
          ) : (
            displayUsers.map((user) => (
              <UserCard
                key={user.id}
                user={user}
                darkMode={darkMode}
                onEdit={() => handleEditUser(user)}
                onDelete={() => handleDeleteUser(user)}
                onViewKeys={() => handleViewKeys(user)}
              />
            ))
          )}
        </div>
      )}

      {/* Modales */}
      <UserEditModal
        isOpen={showEditModal}
        user={editingUser}
        onClose={() => setShowEditModal(false)}
        onSave={handleSaveUser}
        darkMode={darkMode}
      />

      <UserCreateModal
        isOpen={showCreateModal}
        onClose={() => setShowCreateModal(false)}
        onCreate={handleCreateUser}
        darkMode={darkMode}
      />

      <UserDeleteModal
        isOpen={showDeleteModal}
        user={userToDelete}
        onClose={() => setShowDeleteModal(false)}
        onConfirm={handleConfirmDelete}
        darkMode={darkMode}
      />

      <UserKeysModal
        isOpen={showKeysModal}
        onClose={handleCloseKeysModal}
        user={selectedUser}
        userKeys={userKeys}
        isLoading={keysLoading}
        darkMode={darkMode}
      />
    </>
  );
};

UserManagement.propTypes = {
  users: PropTypes.array.isRequired,
  usersWithKeys: PropTypes.array.isRequired,
  isLoading: PropTypes.bool.isRequired,
  error: PropTypes.string,
  onClearError: PropTypes.func.isRequired,
  onUpdateUser: PropTypes.func.isRequired,
  onCreateUser: PropTypes.func.isRequired,
  onDeleteUser: PropTypes.func.isRequired,
  onGetUserKeys: PropTypes.func,
  darkMode: PropTypes.bool.isRequired
};

export default UserManagement;
