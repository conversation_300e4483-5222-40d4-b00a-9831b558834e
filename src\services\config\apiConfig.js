/**
 * API Configuration
 * Configuración centralizada para todas las llamadas a la API
 */

export const API_CONFIG = {
  baseURL: import.meta.env.VITE_API_BASE_URL || 'http://localhost:3000',
  timeout: parseInt(import.meta.env.VITE_API_TIMEOUT) || 30000,
  headers: {
    'Content-Type': 'application/json',
  },
};

export const API_ENDPOINTS = {
  // Auth endpoints
  AUTH: {
    LOGIN: '/auth/login',
    REFRESH: '/auth/refresh',
    PROFILE: '/auth/profile',
  },
  
  // Users endpoints
  USERS: {
    BASE: '/users',
    BY_ID: (id) => `/users/${id}`,
  },
  
  // Keys endpoints
  KEYS: {
    UPLOAD_TO_CTM: '/keys/upload-to-ctm',
    BY_USER: (id) => `/keys/by-user/${id}`,
  },
};

export default API_CONFIG;
