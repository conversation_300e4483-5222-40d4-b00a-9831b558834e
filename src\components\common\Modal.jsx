import { useEffect, useRef, useState } from 'react';
import { createPortal } from 'react-dom';
import { X } from 'lucide-react';
import PropTypes from 'prop-types';
import gsap from 'gsap';
import '../../styles/scrollbar.css';

/**
 * Componente Modal reutilizable
 * Proporciona una base consistente para todos los modales de la aplicación
 */
const Modal = ({
  isOpen,
  onClose,
  title,
  children,
  maxWidth = "max-w-md",
  darkMode = false,
  showCloseButton = true,
  className = ""
}) => {
  const modalRef = useRef(null);
  const overlayRef = useRef(null);
  const [isClosing, setIsClosing] = useState(false);
  // Animaciones GSAP y bloqueo de scroll
  useEffect(() => {
    if (isOpen) {
      // Guardar el scroll actual
      const scrollY = window.scrollY;

      // Bloquear scroll completamente
      document.body.style.position = 'fixed';
      document.body.style.top = `-${scrollY}px`;
      document.body.style.left = '0';
      document.body.style.right = '0';
      document.body.style.overflow = 'hidden';

      // Animación de entrada simple como el proyecto anterior
      if (overlayRef.current && modalRef.current) {
        gsap.fromTo(overlayRef.current,
          { opacity: 0 },
          { opacity: 1, duration: 0.3, ease: "power2.out" }
        );

        gsap.fromTo(modalRef.current,
          { opacity: 0, scale: 0.95, y: 10 },
          { opacity: 1, scale: 1, y: 0, duration: 0.3, ease: "power2.out" }
        );
      }

      return () => {
        // Restaurar scroll al cerrar
        document.body.style.position = '';
        document.body.style.top = '';
        document.body.style.left = '';
        document.body.style.right = '';
        document.body.style.overflow = '';
        window.scrollTo(0, scrollY);
      };
    }
  }, [isOpen]);

  // Cerrar modal al presionar Escape con animación
  useEffect(() => {
    const handleEscape = (e) => {
      if (e.key === 'Escape' && isOpen && !isClosing) {
        handleClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleEscape);
      return () => document.removeEventListener('keydown', handleEscape);
    }
  }, [isOpen, isClosing]);

  // Función para cerrar con animación
  const handleClose = () => {
    if (isClosing) return; // Prevenir múltiples clicks

    setIsClosing(true);

    // Animación de salida
    if (overlayRef.current && modalRef.current) {
      gsap.to(modalRef.current, {
        opacity: 0,
        scale: 0.95,
        y: 10,
        duration: 0.3,
        ease: "power2.in"
      });

      gsap.to(overlayRef.current, {
        opacity: 0,
        duration: 0.3,
        ease: "power2.in",
        onComplete: () => {
          setIsClosing(false);
          onClose();
        }
      });
    } else {
      // Fallback si no hay refs
      setTimeout(() => {
        setIsClosing(false);
        onClose();
      }, 300);
    }
  };

  if (!isOpen && !isClosing) return null;

  // Manejar click en el overlay (fuera del modal)
  const handleOverlayClick = (e) => {
    if (e.target === e.currentTarget) {
      handleClose();
    }
  };

  return createPortal(
    <div
      ref={overlayRef}
      className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center"
      style={{
        zIndex: 10000,
        position: 'fixed',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        width: '100vw',
        height: '100vh'
      }}
      onClick={handleOverlayClick}
    >
      <div
        ref={modalRef}
        className={`rounded-2xl shadow-2xl border ${maxWidth} max-h-[90vh] overflow-y-auto flex flex-col m-4 ${
          darkMode
            ? 'bg-gray-800 text-white border-gray-600'
            : 'bg-white text-gray-900 border-gray-300'
        } ${className}`}
        onClick={(e) => e.stopPropagation()}
        style={{
          backdropFilter: 'blur(10px)',
          background: darkMode
            ? 'rgba(31, 41, 55, 0.95)'
            : 'rgba(255, 255, 255, 0.95)',
          transform: 'none'
        }}
      >
          {/* Header fijo */}
          {(title || showCloseButton) && (
            <div className="flex justify-between items-center p-8 pb-4 border-b border-gray-200 dark:border-gray-600 flex-shrink-0">
              {title && (
                <h3 className="text-xl font-light tracking-wide text-gray-900 dark:text-white">
                  {title}
                </h3>
              )}
              {showCloseButton && (
                <button
                  onClick={handleClose}
                  className="p-1 hover:bg-gray-100 dark:hover:bg-gray-700 rounded transition-colors"
                  aria-label="Cerrar modal"
                >
                  <X size={20} className="text-gray-500 dark:text-gray-400" />
                </button>
              )}
            </div>
          )}

          {/* Contenido con scroll elegante */}
          <div className="flex-1 overflow-y-auto px-8 py-4 custom-scrollbar">
            {children}
          </div>
        </div>
    </div>,
    document.body
  );
};

Modal.propTypes = {
  isOpen: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  title: PropTypes.string,
  children: PropTypes.node.isRequired,
  maxWidth: PropTypes.string,
  darkMode: PropTypes.bool,
  showCloseButton: PropTypes.bool,
  className: PropTypes.string
};

export default Modal;
