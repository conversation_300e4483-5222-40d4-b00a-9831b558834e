/* Estilos modernos para scrollbar */

/* Para navegadores WebKit (Chrome, Safari, Edge) */
.custom-scrollbar::-webkit-scrollbar {
  width: 8px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: transparent;
  border-radius: 10px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: rgba(156, 163, 175, 0.5); /* gray-400 con transparencia */
  border-radius: 10px;
  border: 2px solid transparent;
  background-clip: content-box;
  transition: all 0.2s ease;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: rgba(107, 114, 128, 0.7); /* gray-500 con transparencia */
}

/* Modo oscuro */
.dark .custom-scrollbar::-webkit-scrollbar-thumb {
  background: rgba(75, 85, 99, 0.6); /* gray-600 con transparencia */
}

.dark .custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: rgba(55, 65, 81, 0.8); /* gray-700 con transparencia */
}

/* Para Firefox */
.custom-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: rgba(156, 163, 175, 0.5) transparent;
}

.dark .custom-scrollbar {
  scrollbar-color: rgba(75, 85, 99, 0.6) transparent;
}

/* Estilos adicionales para hacer el scroll más elegante */
.custom-scrollbar {
  scroll-behavior: smooth;
}

/* Ocultar scrollbar cuando no se está usando */
.custom-scrollbar:not(:hover)::-webkit-scrollbar-thumb {
  background: rgba(156, 163, 175, 0.3);
}

.dark .custom-scrollbar:not(:hover)::-webkit-scrollbar-thumb {
  background: rgba(75, 85, 99, 0.4);
}
