import { useState, useRef, useEffect } from 'react';
import { Sun, Moon, User, Mail, Menu, X, LogOut } from 'lucide-react';
import gsap from 'gsap';
import PropTypes from 'prop-types';
import { useLanguage } from '../../i18n';
import Button from './Button';
import LogoutConfirmModal from './LogoutConfirmModal';
import logoUsuario from '../../assets/logo-usuario.png';
import logoAdmin from '../../assets/Logo-admin1.png';

/**
 * Componente MobileMenu - Solo para dispositivos móviles
 * Menú hamburguesa que replica la funcionalidad del Sidebar
 */
const MobileMenu = ({ 
  title,
  currentUser,
  darkMode,
  onToggleDarkMode,
  onLogout,
  navigationItems,
  activeSection,
  onSectionChange
}) => {
  const { t } = useLanguage();
  const [isOpen, setIsOpen] = useState(false);
  const [showLogoutModal, setShowLogoutModal] = useState(false);
  const menuRef = useRef(null);
  const sunIconRef = useRef(null);
  const moonIconRef = useRef(null);

  // Funciones para manejar el menú
  const toggleMenu = () => setIsOpen(!isOpen);
  const closeMenu = () => setIsOpen(false);
  const handleSectionChange = (section) => {
    onSectionChange(section);
    closeMenu();
  };

  // Funciones para manejar logout
  const handleLogoutClick = () => {
    setShowLogoutModal(true);
  };

  const handleLogoutConfirm = () => {
    setShowLogoutModal(false);
    onLogout();
  };

  const handleLogoutCancel = () => {
    setShowLogoutModal(false);
  };

  // Cerrar menú al hacer clic fuera
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (menuRef.current && !menuRef.current.contains(event.target) && isOpen) {
        closeMenu();
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [isOpen]);

  // Animaciones de iconos según el modo (igual que Sidebar)
  useEffect(() => {
    if (darkMode) {
      if (sunIconRef.current) {
        gsap.to(sunIconRef.current, {
          rotation: 360,
          duration: 4,
          ease: "none",
          repeat: -1
        });
      }
    } else {
      if (moonIconRef.current) {
        gsap.to(moonIconRef.current, {
          scale: 1.2,
          opacity: 0.7,
          duration: 1.5,
          ease: "power2.inOut",
          repeat: -1,
          yoyo: true
        });
      }
    }

    return () => {
      if (sunIconRef.current) gsap.killTweensOf(sunIconRef.current);
      if (moonIconRef.current) gsap.killTweensOf(moonIconRef.current);
    };
  }, [darkMode]);

  return (
    <>
      {/* Botón hamburguesa */}
      <button
        onClick={toggleMenu}
        className={`fixed top-4 left-4 z-50 p-3 rounded-xl shadow-lg lg:hidden transition-all duration-300 ${
          darkMode 
            ? 'bg-gray-800 text-white hover:bg-gray-700 border border-gray-600' 
            : 'bg-white text-gray-800 hover:bg-gray-50 border border-gray-200'
        }`}
      >
        {isOpen ? <X size={24} /> : <Menu size={24} />}
      </button>

      {/* Overlay */}
      {isOpen && (
        <div 
          className="fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden"
          onClick={closeMenu}
        />
      )}

      {/* Menú móvil */}
      <aside
        ref={menuRef}
        className={`
          ${isOpen ? 'translate-x-0' : '-translate-x-full'}
          fixed top-0 left-0 w-80 h-full shadow-xl z-50 lg:hidden
          p-6 flex flex-col justify-between transition-all duration-300 ease-in-out
          ${darkMode ? "bg-gray-800 text-white" : "bg-white text-gray-800"}
        `}
      >
        {/* Información del Usuario */}
        <div>
          {/* Logo/Título */}
          {title === "Quantum Admin" ? (
            <div className="mb-4 text-center">
              <div className="w-16 h-16 mx-auto mb-2 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
                <span className="text-white font-bold text-xl">Q</span>
              </div>
              <h2 className="text-xl font-light tracking-wide text-center">
                {title}
              </h2>
            </div>
          ) : (
            <div className="mb-6 text-center">
              <div className="flex items-center justify-center gap-3">
                {/* Icono del usuario al lado del logo SECURE */}
                {currentUser && (
                  <div className="w-12 h-12 rounded-full overflow-hidden shadow-lg border-2 border-white dark:border-gray-600">
                    <img
                      src={logoUsuario}
                      alt="Logo Usuario"
                      className="w-full h-full object-cover"
                    />
                  </div>
                )}
                {/* Logo SECURE horizontal */}
                <img
                  src="/src/assets/sequre-logo-negro.svg"
                  alt="SECURE Logo"
                  className={`w-28 h-8 object-contain ${darkMode ? 'filter invert' : ''}`}
                />
              </div>
            </div>
          )}

          {/* Información del usuario */}
          {currentUser && (
            <div className={`mb-8 p-4 rounded-xl border ${darkMode ? 'bg-gray-700 border-gray-600' : 'bg-gray-50 border-gray-200'} shadow-lg`}>
              <div className="flex items-center gap-3 mb-2">
                <User size={16} className="text-blue-500" />
                <span className="text-sm font-light tracking-wide">
                  {currentUser.role === 'ADMIN' ? 'Super Admin' : `${currentUser.firstName} ${currentUser.lastName}`}
                </span>
              </div>
              <div className="flex items-center gap-3">
                <Mail size={16} className="text-green-500" />
                <span className="text-xs font-light tracking-wide text-gray-600 dark:text-gray-300">
                  {currentUser.email}
                </span>
              </div>
            </div>
          )}

          {/* Navegación */}
          <nav className="space-y-3">
            {navigationItems.map((item) => (
              <button
                key={item.key}
                onClick={() => handleSectionChange(item.key)}
                className={`flex items-center gap-3 px-4 py-3 rounded-xl transition-all duration-300 w-full text-left font-light tracking-wide ${
                  activeSection === item.key
                    ? (darkMode ? 'bg-blue-900 text-blue-300 shadow-lg' : 'bg-blue-100 text-blue-700 shadow-lg')
                    : (darkMode ? 'hover:bg-gray-700 hover:shadow-md' : 'hover:bg-gray-100 hover:shadow-md')
                }`}
              >
                <item.icon size={18} /> <span>{item.label}</span>
              </button>
            ))}
          </nav>
        </div>

        {/* Controles inferiores */}
        <div className="space-y-3">
          {/* Botón de modo oscuro */}
          <button
            onClick={onToggleDarkMode}
            className={`w-full flex items-center justify-center gap-3 py-3 px-4 rounded-xl font-light tracking-wide shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 group ${
              darkMode
                ? "bg-gradient-to-r from-yellow-400 to-yellow-600 hover:from-yellow-500 hover:to-yellow-700 text-gray-900"
                : "bg-gradient-to-r from-gray-500 to-gray-600 hover:from-gray-600 hover:to-gray-700 text-white"
            }`}
          >
            <div className={`p-1 rounded-lg group-hover:bg-white/30 transition-all duration-300 ${
              darkMode ? "bg-gray-900/20" : "bg-white/20"
            }`}>
              {darkMode ? (
                <Sun ref={sunIconRef} size={16} className="text-gray-900" />
              ) : (
                <Moon ref={moonIconRef} size={16} className="text-white" />
              )}
            </div>
            <span className="font-light tracking-wide">
              {darkMode ? t('sidebar.lightMode') : t('sidebar.darkMode')}
            </span>
          </button>

          {/* Botón de logout */}
          <button
            onClick={handleLogoutClick}
            className="w-full flex items-center justify-center gap-3 py-3 px-4 rounded-xl font-light tracking-wide shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 group bg-gradient-to-r from-red-600 to-red-700 hover:from-red-700 hover:to-red-800 text-white"
          >
            <div className="p-1 bg-white/20 rounded-lg group-hover:bg-white/30 transition-all duration-300">
              <LogOut size={16} />
            </div>
            <span className="font-light tracking-wide">
              {t('sidebar.logout')}
            </span>
          </button>
        </div>
      </aside>

      {/* Modal de confirmación de logout */}
      <LogoutConfirmModal
        isOpen={showLogoutModal}
        onConfirm={handleLogoutConfirm}
        onCancel={handleLogoutCancel}
        darkMode={darkMode}
      />
    </>
  );
};

MobileMenu.propTypes = {
  title: PropTypes.string.isRequired,
  currentUser: PropTypes.object,
  darkMode: PropTypes.bool.isRequired,
  onToggleDarkMode: PropTypes.func.isRequired,
  onLogout: PropTypes.func.isRequired,
  navigationItems: PropTypes.arrayOf(PropTypes.shape({
    key: PropTypes.string.isRequired,
    label: PropTypes.string.isRequired,
    icon: PropTypes.elementType.isRequired
  })).isRequired,
  activeSection: PropTypes.string.isRequired,
  onSectionChange: PropTypes.func.isRequired
};

export default MobileMenu;
