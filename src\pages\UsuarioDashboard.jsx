import { useState, useEffect } from "react";
import {
  Key,
  User,
  Settings,
  BarChart3,
  Calendar,
  Shield,
} from "lucide-react";
import { useAuth, useKeys, useUsers } from '../hooks/index';
import { DashboardLayout, DashboardStats } from '../components/dashboard';
import { KeyManagement, ProfileManagement } from '../components/dashboard/user';

const UsuarioDashboard = () => {
  const [activeSection, setActiveSection] = useState('dashboard');

  // Hooks para autenticación, llaves y usuarios
  const { user: currentUser, logout } = useAuth();
  const {
    keys: userKeys,
    statistics,
    isLoading: keysLoading,
    error: keysError,
    uploadToCTM,
    getKeysByUser,
    deleteKey,
    clearError
  } = useKeys();
  const {
    updateUser,
    isLoading: userLoading,
    error: userError,
    clearError: clearUserError
  } = useUsers();

  // <PERSON>gar llaves del usuario al montar el componente
  useEffect(() => {
    const loadUserKeys = async () => {
      if (currentUser && currentUser.id) {
        try {
          await getKeysByUser(currentUser.id);
        } catch {
          // Error loading user keys - handled by hooks
        }
      }
    };

    loadUserKeys();
  }, [currentUser, getKeysByUser]);

  // Manejar logout
  const handleLogout = () => {
    logout();
    
    // Limpiar storage
    sessionStorage.removeItem('token');
    sessionStorage.removeItem('userRole');
    sessionStorage.removeItem('tokenExpiry');
    sessionStorage.removeItem('loginTime');
    localStorage.removeItem('token');
    localStorage.removeItem('userRole');
    sessionStorage.setItem('wasLoggedOut', 'true');
    
    window.location.href = '/';
  };

  // Funciones para manejar llaves
  const handleUploadKey = async (keyData) => {
    const result = await uploadToCTM(keyData);
    // Recargar la lista de llaves después de subir exitosamente
    if (currentUser && currentUser.id) {
      await getKeysByUser(currentUser.id);
    }
    return result;
  };

  const handleDeleteKey = async (keyId) => {
    await deleteKey(keyId);
  };

  // Función para actualizar perfil
  const handleUpdateProfile = async (profileData) => {
    await updateUser(currentUser.id, profileData);
    // Recargar el perfil del usuario para mostrar los cambios
    window.location.reload();
  };

  // Función para cambiar contraseña
  const handleChangePassword = async (passwordData) => {
    // Aquí deberías implementar la llamada al servicio de cambio de contraseña
    // Por ahora, simularemos la funcionalidad
    try {
      // Ejemplo de implementación:
      // await authService.changePassword(passwordData.currentPassword, passwordData.newPassword);
      console.log('Cambio de contraseña:', passwordData);

      // Mostrar mensaje de éxito (puedes implementar un toast o notificación)
      alert('Contraseña cambiada exitosamente');
    } catch (error) {
      console.error('Error al cambiar contraseña:', error);
      throw error;
    }
  };

  // Configuración de navegación
  const navigationItems = [
    { key: 'dashboard', label: 'Dashboard', icon: BarChart3 },
    { key: 'keys', label: 'Mis Llaves', icon: Key },
    { key: 'profile', label: 'Mi Perfil', icon: User },
    { key: 'settings', label: 'Configuración', icon: Settings }
  ];

  // Configuración de estadísticas principales
  const mainStats = [
    {
      icon: Key,
      title: "Mis Llaves",
      value: statistics?.total || userKeys.length,
      description: "Llaves cuánticas generadas.",
      iconColor: "text-blue-500",
      valueColor: "text-blue-600 dark:text-blue-400"
    },
    {
      icon: Shield,
      title: "Llaves Activas",
      value: statistics?.uploadedToCtm || userKeys.filter(key =>
        key.status === 'UPLOADED_TO_CTM' || key.uploadedToCtm === true
      ).length,
      description: "Llaves en funcionamiento.",
      iconColor: "text-green-500",
      valueColor: "text-green-600 dark:text-green-400"
    },
    {
      icon: Calendar,
      title: "Último Acceso",
      value: new Date().toLocaleDateString('es-ES', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric'
      }),
      description: "Fecha de último login.",
      iconColor: "text-purple-500",
      valueColor: "text-purple-600 dark:text-purple-400"
    }
  ];

  // Configuración de estadísticas adicionales
  const miniStats = statistics ? [
    {
      value: statistics.successful,
      label: "Exitosas",
      valueColor: "text-green-600 dark:text-green-400"
    },
    {
      value: statistics.failed,
      label: "Fallidas",
      valueColor: "text-red-600 dark:text-red-400"
    },
    {
      value: statistics.uploadedToCtm,
      label: "En CTM",
      valueColor: "text-blue-600 dark:text-blue-400"
    },
    {
      value: (() => {
        if (!statistics.total || statistics.total === 0) return 0;
        const percentage = Math.round((statistics.successful / statistics.total) * 100);
        return Math.min(percentage, 100); // Asegurar que no exceda 100%
      })(),
      label: "% Éxito",
      valueColor: "text-purple-600 dark:text-purple-400"
    }
  ] : [];

  const renderContent = (darkMode = false) => {
    switch (activeSection) {
      case 'dashboard':
        return (
          <>
            <h1 className="text-3xl font-light tracking-wide leading-relaxed mb-4 text-gray-900 dark:text-white">
              Bienvenido, {currentUser ? `${currentUser.firstName} ${currentUser.lastName}` : 'Usuario'}
            </h1>
            <p className="text-gray-600 dark:text-gray-300 mb-6 font-light tracking-wide">
              Aquí puedes gestionar tus llaves cuánticas y actualizar tu información personal.
            </p>

            <DashboardStats
              mainStats={mainStats}
              miniStats={miniStats}
              isLoading={keysLoading}
              error={keysError}
              onClearError={clearError}
            />
          </>
        );

      case 'keys':
        return (
          <KeyManagement
            keys={userKeys}
            isLoading={keysLoading}
            error={keysError}
            onClearError={clearError}
            onUploadKey={handleUploadKey}
            onDeleteKey={handleDeleteKey}
            darkMode={darkMode}
          />
        );

      case 'profile':
        return (
          <ProfileManagement
            currentUser={currentUser}
            isLoading={userLoading}
            error={userError}
            onClearError={clearUserError}
            onUpdateProfile={handleUpdateProfile}
            onChangePassword={handleChangePassword}
            darkMode={darkMode}
          />
        );

      case 'settings':
        return (
          <div>
            <h1 className="text-3xl font-bold mb-4">Configuración</h1>
            <p className="text-gray-600 dark:text-gray-300">
              Ajustes de cuenta y preferencias del sistema.
            </p>

            <div className={`mt-6 p-6 rounded-lg border bg-white dark:bg-gray-700 dark:border-gray-600`}>
              <h3 className="text-lg font-semibold mb-4">Preferencias</h3>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <span>Modo Oscuro</span>
                  <span className="text-sm text-gray-500">
                    Controlado por el botón en la barra lateral
                  </span>
                </div>
              </div>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  // Componente wrapper que recibe darkMode del DashboardLayout
  const ContentWrapper = ({ darkMode }) => {
    return renderContent(darkMode);
  };

  return (
    <DashboardLayout
      title="Quantum Usuario"
      currentUser={currentUser}
      navigationItems={navigationItems}
      activeSection={activeSection}
      onSectionChange={setActiveSection}
      onLogout={handleLogout}
      expectedRole={['usuario', 'USER', 'user']}
    >
      <ContentWrapper />
    </DashboardLayout>
  );
};

export default UsuarioDashboard;
