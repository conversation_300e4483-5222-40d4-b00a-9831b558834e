import { useState, useEffect } from "react";
import {
  Users,
  KeyRound,
  Settings,
  BarChart3,
  Activity,
} from "lucide-react";
import { useAuth, useUsers } from '../hooks/index';
import { useAdminStats } from '../hooks/useAdminStats';
import { DashboardLayout, DashboardStats } from '../components/dashboard';
import { UserManagement } from '../components/dashboard/admin';
import { useLanguage } from '../i18n';

const AdminDashboard = () => {
  const [activeSection, setActiveSection] = useState('dashboard');

  // Hooks para autenticación y usuarios
  const { user: currentUser, logout } = useAuth();
  const { t } = useLanguage();
  const {
    users,
    isLoading: usersLoading,
    error: usersError,
    getAllUsers,
    createUser,
    updateUser,
    deleteUser,
    clearError
  } = useUsers();

  // Hook para estadísticas del admin
  const {
    stats,
    usersWithKeys,
    isLoading: statsLoading,
    error: statsError,
    loadStats,
    clearError: clearStatsError
  } = useAdminStats();

  // Cargar usuarios y estadísticas al montar el componente
  useEffect(() => {
    const loadData = async () => {
      try {
        // Cargar usuarios y estadísticas en paralelo
        await Promise.all([
          getAllUsers(),
          loadStats()
        ]);
      } catch {
        // Error loading admin data - handled by hooks
      }
    };

    if (currentUser && (currentUser.role === 'ADMIN' || currentUser.role === 'admin')) {
      loadData();
    }
  }, [currentUser, getAllUsers, loadStats]);

  // Manejar logout
  const handleLogout = () => {
    logout();
  };

  // Funciones para gestión de usuarios
  const handleUpdateUser = async (userId, userData) => {
    await updateUser(userId, userData);
    // Refrescar datos después de actualizar
    await Promise.all([getAllUsers(), loadStats()]);
  };

  const handleCreateUser = async (userData) => {
    await createUser(userData);
    // Refrescar datos después de crear
    await Promise.all([getAllUsers(), loadStats()]);
  };

  const handleDeleteUser = async (userId) => {
    try {
      // Usar el hook deleteUser que ya maneja la actualización de la lista
      await deleteUser(userId);

      // Refrescar estadísticas después de eliminar
      await loadStats();

      // TODO: Mostrar notificación elegante de éxito (reemplazar alert)
      alert('Usuario eliminado exitosamente');

    } catch (error) {
      console.error('Error eliminando usuario:', error);
      // TODO: Mostrar notificación elegante de error (reemplazar alert)
      alert('Error al eliminar el usuario: ' + error.message);
    }
  };

  const handleGetUserKeys = async (userId) => {
    try {
      // TODO: Implementar endpoint específico para obtener llaves de un usuario
      // Por ahora, buscar en usersWithKeys si el usuario tiene llaves
      const userWithKeys = usersWithKeys.find(u => u.id === userId);
      return userWithKeys?.keys || [];
    } catch (error) {
      console.error('Error getting user keys:', error);
      return [];
    }
  };

  // Configuración de navegación
  const navigationItems = [
    { key: 'dashboard', label: t('navigation.dashboard'), icon: BarChart3 },
    { key: 'users', label: t('navigation.users'), icon: Users },
    { key: 'keys', label: 'Llaves', icon: KeyRound }
  ];

  // Configuración de estadísticas principales
  const mainStats = [
    {
      icon: Users,
      title: t('dashboard.totalUsers'),
      value: stats.totalUsers,
      description: "Usuarios registrados en el sistema.",
      iconColor: "text-blue-500",
      valueColor: "text-blue-600 dark:text-blue-400"
    },
    {
      icon: KeyRound,
      title: t('dashboard.activeKeys'),
      value: stats.activeKeys,
      description: "Llaves subidas a CTM exitosamente.",
      iconColor: "text-green-500",
      valueColor: "text-green-600 dark:text-green-400"
    },
    {
      icon: Activity,
      title: t('dashboard.activeUsers'),
      value: stats.activeUsers,
      description: "Usuarios con estado activo.",
      iconColor: "text-purple-500",
      valueColor: "text-purple-600 dark:text-purple-400"
    }
  ];

  // Configuración de estadísticas adicionales
  const miniStats = [
    {
      value: stats.totalKeys,
      label: t('dashboard.totalKeys'),
      valueColor: "text-gray-900 dark:text-white"
    },
    {
      value: stats.successfulKeys,
      label: t('dashboard.successful'),
      valueColor: "text-green-600 dark:text-green-400"
    },
    {
      value: stats.failedKeys,
      label: t('dashboard.failed'),
      valueColor: "text-red-600 dark:text-red-400"
    },
    {
      value: stats.uploadedToCtm,
      label: t('dashboard.inCTM'),
      valueColor: "text-blue-600 dark:text-blue-400"
    }
  ];

  const renderContent = (darkMode = false) => {
    switch (activeSection) {
      case 'dashboard':
        return (
          <>
            <h1 className="text-3xl font-light tracking-wide leading-relaxed mb-4">
              {t('dashboard.adminWelcome')}
            </h1>
            <p className="text-gray-600 dark:text-white mb-6 font-light tracking-wide">
              {t('dashboard.adminSubtitle')}
            </p>

            <DashboardStats
              mainStats={mainStats}
              miniStats={miniStats}
              isLoading={statsLoading}
              error={statsError}
              onClearError={clearStatsError}
            />
          </>
        );

      case 'users':
        return (
          <UserManagement
            users={users}
            usersWithKeys={usersWithKeys}
            isLoading={usersLoading || statsLoading}
            error={usersError}
            onClearError={clearError}
            onUpdateUser={handleUpdateUser}
            onCreateUser={handleCreateUser}
            onDeleteUser={handleDeleteUser}
            onGetUserKeys={handleGetUserKeys}
            darkMode={darkMode}
          />
        );

      case 'keys':
        return (
          <div>
            <h1 className="text-3xl font-bold mb-4">Gestión de Llaves</h1>
            <p className="text-gray-600 dark:text-white">
              Sección en desarrollo para gestionar todas las llaves API del sistema.
            </p>
          </div>
        );



      default:
        return null;
    }
  };

  // Componente wrapper que recibe darkMode del DashboardLayout
  const ContentWrapper = ({ darkMode }) => {
    return renderContent(darkMode);
  };

  return (
    <DashboardLayout
      title="Quantum Admin"
      currentUser={currentUser}
      navigationItems={navigationItems}
      activeSection={activeSection}
      onSectionChange={setActiveSection}
      onLogout={handleLogout}
      expectedRole={['admin', 'ADMIN']}
    >
      <ContentWrapper />
    </DashboardLayout>
  );
};

export default AdminDashboard;
