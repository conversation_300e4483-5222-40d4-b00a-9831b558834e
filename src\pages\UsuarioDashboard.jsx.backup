import { useEffect, useRef, useState } from "react";
import gsap from "gsap";
import {
  Sun,
  Moon,
  Key,
  User,
  Settings,
  BarChart3,
  Plus,
  Edit,
  Trash2,
  X,
  Save,
  Mail,
  Calendar,
  Shield,
  Upload,
} from "lucide-react";
import secureStorage from '../utils/SecureStorage';
import securityMonitor from '../utils/SecurityMonitor';
import { useAuth, useKeys, useUsers } from '../hooks/index';

const UsuarioDashboard = () => {
  const sidebarRef = useRef(null);
  const mainContentRef = useRef(null);
  const [darkMode, setDarkMode] = useState(false);
  const [activeSection, setActiveSection] = useState('dashboard');
  const [showEditProfile, setShowEditProfile] = useState(false);
  const [showGenerateKey, setShowGenerateKey] = useState(false);
  const [isSavingProfile, setIsSavingProfile] = useState(false);
  const [editingProfile, setEditingProfile] = useState({
    firstName: '',
    lastName: '',
    email: '',
    company: ''
  });
  const [keyData, setKeyData] = useState({
    key_name: '',
    algorithm: 'AES',
    num_bytes: 32,
    exportable: false,
  });

  // Hooks para autenticación, llaves y usuarios
  const { user: currentUser, logout } = useAuth();
  const {
    keys: userKeys,
    statistics,
    isLoading: keysLoading,
    error: keysError,
    uploadToCTM,
    getKeysByUser,
    deleteKey,
    clearError
  } = useKeys();
  const {
    updateUser,
    isLoading: userLoading,
    error: userError,
    clearError: clearUserError
  } = useUsers();

  // Cargar llaves del usuario al montar el componente
  useEffect(() => {
    const loadUserKeys = async () => {
      if (currentUser && currentUser.id) {
        try {
          await getKeysByUser(currentUser.id);
        } catch (error) {
          console.error('Error loading user keys:', error);
        }
      }
    };

    loadUserKeys();
  }, [currentUser, getKeysByUser]);

  // Usar llaves del backend directamente (sin fallback)
  const displayKeys = userKeys;

  // Funciones para manejar llaves
  const handleUploadKey = async () => {
    try {
      const result = await uploadToCTM(keyData);
      console.log('Llave subida exitosamente:', result);
      setShowGenerateKey(false);
      // Resetear formulario
      setKeyData({
        key_name: '',
        algorithm: 'AES',
        num_bytes: 32,
        exportable: false,
      });
    } catch (error) {
      console.error('Error uploading key:', error);
    }
  };

  const handleKeyDataChange = (field, value) => {
    setKeyData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const generateRandomKey = () => {
    const bytes = new Uint8Array(keyData.num_bytes);
    crypto.getRandomValues(bytes);
    const base64 = btoa(String.fromCharCode(...bytes));
    handleKeyDataChange('key_material_base64', base64);
  };

  // 🔁 Animaciones GSAP al montar
  useEffect(() => {
    if (window.innerWidth >= 768) {
      gsap.fromTo(
        sidebarRef.current,
        { x: -100, opacity: 0 },
        { x: 0, opacity: 1, duration: 1, ease: "power2.out" }
      );
    }

    gsap.fromTo(
      mainContentRef.current,
      { y: 100, opacity: 0 },
      { y: 0, opacity: 1, duration: 1, ease: "power2.out", delay: 0.3 }
    );
  }, []);

  // 🛡️ Inicializar monitoreo de seguridad para usuario
  useEffect(() => {
    securityMonitor.start();

    console.log('[USER_SECURITY] User dashboard accessed at:', new Date().toISOString());
    console.log('[USER_SECURITY] User info:', currentUser);

    // Verificar integridad de datos de usuario
    const userRole = secureStorage.getSecure('user_role') || sessionStorage.getItem('userRole');
    if (userRole !== 'usuario' && userRole !== 'USER' && userRole !== 'user') {
      console.error('[USER_SECURITY] Invalid user role detected:', userRole);
      handleLogout();
    }

    return () => {
      securityMonitor.stop();
    };
  }, [currentUser]);

  // 🔁 Toggle de modo oscuro
  const toggleDarkMode = () => {
    const newDarkMode = !darkMode;
    setDarkMode(newDarkMode);

    // Agregar/quitar clase 'dark' del HTML para que Tailwind funcione
    if (newDarkMode) {
      document.documentElement.classList.add('dark');
    } else {
      document.documentElement.classList.remove('dark');
    }
  };

  // 🔁 Cerrar sesión seguro
  const handleLogout = () => {
    console.log('[USER_SECURITY] User logout initiated at:', new Date().toISOString());

    securityMonitor.stop();
    logout(); // Usar el logout del hook

    sessionStorage.removeItem('token');
    sessionStorage.removeItem('userRole');
    sessionStorage.removeItem('tokenExpiry');
    sessionStorage.removeItem('loginTime');
    localStorage.removeItem('token');
    localStorage.removeItem('userRole');

    sessionStorage.setItem('wasLoggedOut', 'true');

    console.log('[USER_SECURITY] User logout completed at:', new Date().toISOString());

    window.location.href = '/';
  };



  // 👤 Manejar edición de perfil
  const handleEditProfile = () => {
    if (currentUser) {
      setEditingProfile({
        firstName: currentUser.firstName || '',
        lastName: currentUser.lastName || '',
        email: currentUser.email || '',
        company: currentUser.company || ''
      });
      setShowEditProfile(true);
    }
  };

  // 👤 Actualizar perfil de usuario
  const updateProfile = async () => {
    try {
      setIsSavingProfile(true);

      // Solo enviar los campos que el usuario puede modificar
      const allowedFields = {
        firstName: editingProfile.firstName,
        lastName: editingProfile.lastName,
        email: editingProfile.email,
        company: editingProfile.company
      };

      await updateUser(currentUser.id, allowedFields);
      setShowEditProfile(false);

      // Recargar el perfil del usuario para mostrar los cambios
      window.location.reload();
    } catch (error) {
      console.error('Error updating profile:', error);
    } finally {
      setIsSavingProfile(false);
    }
  };

  // 👤 Manejar cambios en el formulario de perfil
  const handleProfileChange = (field, value) => {
    setEditingProfile({ ...editingProfile, [field]: value });
  };

  // 🗑️ Eliminar llave
  const handleDeleteKey = async (keyId, keyName) => {
    if (confirm(`¿Estás seguro de que deseas eliminar la llave "${keyName}"? Esta acción no se puede deshacer.`)) {
      try {
        await deleteKey(keyId);
        // Opcional: mostrar mensaje de éxito
        console.log('[USER_SECURITY] Key deleted successfully:', keyId);
      } catch (error) {
        console.error('[USER_SECURITY] Error deleting key:', error);
        alert('Error al eliminar la llave: ' + error.message);
      }
    }
  };

  return (
    <div
      className={`flex h-screen transition-colors duration-500 ${
        darkMode ? "bg-gray-700 text-white" : "bg-gray-100 text-gray-900"
      }`}
    >
      {/* Sidebar siempre visible (idéntico al admin) */}
      <aside
        ref={sidebarRef}
        className={`w-64 sm:w-72 h-full shadow-xl z-10 p-6 flex flex-col justify-between transition-colors duration-500
        ${darkMode ? "bg-gray-800 text-white" : "bg-white text-gray-800"}`}
      >
        {/* Información del Usuario */}
        <div>
          <h2 className="text-2xl font-bold mb-4 text-center">Quantum Usuario</h2>

          {/* Información del usuario */}
          {currentUser && (
            <div className={`mb-8 p-4 rounded-lg border ${darkMode ? 'bg-gray-700 border-gray-600' : 'bg-gray-50 border-gray-200'}`}>
              <div className="flex items-center gap-3 mb-2">
                <User size={16} className="text-blue-500" />
                <span className="font-medium text-sm">
                  {currentUser.firstName} {currentUser.lastName}
                </span>
              </div>
              <div className="flex items-center gap-3">
                <Mail size={16} className="text-green-500" />
                <span className={`text-sm ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                  {currentUser.email}
                </span>
              </div>
            </div>
          )}

          <nav className="space-y-4">
            <button
              onClick={() => setActiveSection('dashboard')}
              className={`flex items-center gap-3 px-4 py-2 rounded-lg transition-colors duration-300 w-full text-left ${
                activeSection === 'dashboard'
                  ? (darkMode ? 'bg-blue-900 text-blue-300' : 'bg-blue-100 text-blue-700')
                  : (darkMode ? 'hover:bg-gray-700' : 'hover:bg-gray-100')
              }`}
            >
              <BarChart3 size={18} /> <span>Dashboard</span>
            </button>
            <button
              onClick={() => setActiveSection('keys')}
              className={`flex items-center gap-3 px-4 py-2 rounded-lg transition-colors duration-300 w-full text-left ${
                activeSection === 'keys'
                  ? (darkMode ? 'bg-blue-900 text-blue-300' : 'bg-blue-100 text-blue-700')
                  : (darkMode ? 'hover:bg-gray-700' : 'hover:bg-gray-100')
              }`}
            >
              <Key size={18} /> <span>Mis Llaves</span>
            </button>
            <button
              onClick={() => setActiveSection('profile')}
              className={`flex items-center gap-3 px-4 py-2 rounded-lg transition-colors duration-300 w-full text-left ${
                activeSection === 'profile'
                  ? (darkMode ? 'bg-blue-900 text-blue-300' : 'bg-blue-100 text-blue-700')
                  : (darkMode ? 'hover:bg-gray-700' : 'hover:bg-gray-100')
              }`}
            >
              <User size={18} /> <span>Mi Perfil</span>
            </button>
            <button
              onClick={() => setActiveSection('settings')}
              className={`flex items-center gap-3 px-4 py-2 rounded-lg transition-colors duration-300 w-full text-left ${
                activeSection === 'settings'
                  ? (darkMode ? 'bg-blue-900 text-blue-300' : 'bg-blue-100 text-blue-700')
                  : (darkMode ? 'hover:bg-gray-700' : 'hover:bg-gray-100')
              }`}
            >
              <Settings size={18} /> <span>Configuración</span>
            </button>
          </nav>
        </div>

        {/* Botones: modo claro/oscuro + logout */}
        <div className="flex flex-col gap-4">
          <button
            onClick={toggleDarkMode}
            className={`flex items-center justify-center gap-2 w-full px-4 py-2 rounded-lg text-sm font-medium border transition duration-300
              ${darkMode
                ? "border-gray-500 text-white hover:bg-gray-700/40"
                : "border-gray-400 text-gray-800 hover:bg-gray-100"}`}
          >
            {darkMode ? (
              <>
                <Sun size={18} className="text-yellow-400 animate-pulse" />
                <span>Modo Claro</span>
              </>
            ) : (
              <>
                <Moon size={18} className="text-indigo-300 animate-pulse" />
                <span>Modo Oscuro</span>
              </>
            )}
          </button>

          <button
            onClick={handleLogout}
            className="bg-red-600 hover:bg-red-700 transition text-white font-semibold px-4 py-2 rounded-lg shadow text-center"
          >
            Cerrar sesión
          </button>
        </div>
      </aside>

      {/* Contenido Principal */}
      <main
        ref={mainContentRef}
        className={`flex-1 p-6 sm:p-10 overflow-y-auto relative transition-colors duration-500
          ${darkMode ? "bg-gray-700" : "bg-gray-50"}`}
      >
        {/* Contenedor principal */}
        <div
          className={`relative z-10 transition-all duration-500 rounded-xl p-6 sm:p-8 shadow-md border
            ${darkMode
              ? "bg-gray-800 border-gray-700 text-white"
              : "bg-white border-gray-200 text-gray-900"}`}
        >
          {/* Dashboard Principal */}
          {activeSection === 'dashboard' && (
            <>
              <h1 className="text-3xl font-bold mb-4">
                Bienvenido, {currentUser ? `${currentUser.firstName} ${currentUser.lastName}` : 'Usuario'}
              </h1>
              <p className="text-gray-600 dark:text-gray-300 mb-6">
                Aquí puedes gestionar tus llaves cuánticas y actualizar tu información personal.
              </p>

              {/* Tarjetas de información */}
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
                <div className="p-6 rounded-lg border bg-white dark:bg-gray-700 dark:border-gray-600 shadow">
                  <div className="flex items-center gap-3 mb-2">
                    <Key size={20} className="text-blue-500" />
                    <span className="font-semibold text-gray-900 dark:text-white">Mis Llaves</span>
                  </div>
                  <p className="text-2xl font-bold text-blue-600 dark:text-blue-400">
                    {statistics?.total || displayKeys.length}
                  </p>
                  <p className="text-sm text-gray-600 dark:text-gray-300">
                    Llaves cuánticas generadas.
                  </p>
                </div>

                <div className="p-6 rounded-lg border bg-white dark:bg-gray-700 dark:border-gray-600 shadow">
                  <div className="flex items-center gap-3 mb-2">
                    <Shield size={20} className="text-green-500" />
                    <span className="font-semibold text-gray-900 dark:text-white">Llaves Activas</span>
                  </div>
                  <p className="text-2xl font-bold text-green-600 dark:text-green-400">
                    {statistics?.uploadedToCtm || userKeys.filter(key =>
                      key.status === 'UPLOADED_TO_CTM' || key.uploadedToCtm === true
                    ).length}
                  </p>
                  <p className="text-sm text-gray-600 dark:text-gray-300">
                    Llaves en funcionamiento.
                  </p>
                </div>

                <div className="p-6 rounded-lg border bg-white dark:bg-gray-700 dark:border-gray-600 shadow">
                  <div className="flex items-center gap-3 mb-2">
                    <Calendar size={20} className="text-purple-500" />
                    <span className="font-semibold text-gray-900 dark:text-white">Último Acceso</span>
                  </div>
                  <p className="text-lg font-bold text-purple-600 dark:text-purple-400">
                    {new Date().toLocaleDateString()}
                  </p>
                  <p className="text-sm text-gray-600 dark:text-gray-300">
                    Fecha de último login.
                  </p>
                </div>
              </div>

              {/* Estadísticas adicionales */}
              {statistics && (
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mt-6">
                  <div className="p-4 rounded-lg border bg-white dark:bg-gray-700 dark:border-gray-600 shadow">
                    <div className="text-center">
                      <p className="text-lg font-bold text-green-600 dark:text-green-400">
                        {statistics.successful}
                      </p>
                      <p className="text-sm text-gray-600 dark:text-gray-300">Exitosas</p>
                    </div>
                  </div>

                  <div className="p-4 rounded-lg border bg-white dark:bg-gray-700 dark:border-gray-600 shadow">
                    <div className="text-center">
                      <p className="text-lg font-bold text-red-600 dark:text-red-400">
                        {statistics.failed}
                      </p>
                      <p className="text-sm text-gray-600 dark:text-gray-300">Fallidas</p>
                    </div>
                  </div>

                  <div className="p-4 rounded-lg border bg-white dark:bg-gray-700 dark:border-gray-600 shadow">
                    <div className="text-center">
                      <p className="text-lg font-bold text-blue-600 dark:text-blue-400">
                        {statistics.uploadedToCtm}
                      </p>
                      <p className="text-sm text-gray-600 dark:text-gray-300">En CTM</p>
                    </div>
                  </div>

                  <div className="p-4 rounded-lg border bg-white dark:bg-gray-700 dark:border-gray-600 shadow">
                    <div className="text-center">
                      <p className="text-lg font-bold text-purple-600 dark:text-purple-400">
                        {Math.round((statistics.successful / statistics.total) * 100) || 0}%
                      </p>
                      <p className="text-sm text-gray-600 dark:text-gray-300">Éxito</p>
                    </div>
                  </div>
                </div>
              )}
            </>
          )}

          {/* Sección de Llaves */}
          {activeSection === 'keys' && (
            <>
              <div className="flex justify-between items-center mb-6">
                <h1 className="text-3xl font-bold">Mis Llaves Cuánticas</h1>
                <button
                  onClick={() => setShowGenerateKey(true)}
                  className="flex items-center gap-2 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition"
                >
                  <Plus size={16} />
                  Subir Nueva Llave
                </button>
              </div>

              {/* Mostrar errores */}
              {keysError && (
                <div className="mb-4 p-4 bg-red-100 border border-red-400 text-red-700 rounded-lg">
                  Error: {keysError}
                  <button
                    onClick={clearError}
                    className="ml-2 text-red-500 hover:text-red-700"
                  >
                    ✕
                  </button>
                </div>
              )}

              {/* Mostrar loading */}
              {keysLoading && (
                <div className="flex items-center justify-center py-8">
                  <div className="w-8 h-8 border-4 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
                  <span className="ml-3">Cargando llaves...</span>
                </div>
              )}

              {/* Lista de llaves */}
              {!keysLoading && (
                <div className="space-y-4">
                  {displayKeys.length === 0 ? (
                    <div className="text-center py-8 text-gray-500">
                      No tienes llaves registradas. ¡Crea tu primera llave!
                    </div>
                  ) : (
                    displayKeys.map((key) => (
                  <div
                    key={key.id}
                    className={`p-6 rounded-lg border shadow-sm ${
                      darkMode ? 'bg-gray-700 border-gray-600' : 'bg-white border-gray-200'
                    }`}
                  >
                    <div className="flex justify-between items-start">
                      <div className="flex-1">
                        <div className="flex items-center gap-4 mb-3">
                          <h3 className="text-xl font-semibold">{key.name}</h3>
                          <span
                            className={`px-3 py-1 rounded-full text-xs font-medium ${
                              key.status === 'UPLOADED_TO_CTM' || key.uploadedToCtm === true
                                ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                                : key.status === 'FAILED' || key.isSuccessful === false
                                ? 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
                                : 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
                            }`}
                          >
                            {key.status === 'UPLOADED_TO_CTM' ? 'Activa' :
                             key.status === 'FAILED' ? 'Fallida' :
                             key.status || 'Pendiente'}
                          </span>
                          <span className="px-2 py-1 rounded text-xs bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                            {key.type}
                          </span>
                        </div>

                        <div className="mb-4">
                          <p className="font-mono text-sm bg-gray-100 dark:bg-gray-600 p-2 rounded">
                            {key.id}
                          </p>
                        </div>

                        <div className="grid grid-cols-2 gap-4 text-sm">
                          <div>
                            <span className="text-gray-600 dark:text-gray-300">Creada:</span>
                            <p className="font-medium">
                              {key.createdAt ? new Date(key.createdAt).toLocaleDateString() : 'N/A'}
                            </p>
                          </div>
                          <div>
                            <span className="text-gray-600 dark:text-gray-300">Algoritmo:</span>
                            <p className="font-medium">{key.algorithm || key.type || 'N/A'}</p>
                          </div>
                        </div>
                      </div>

                      {/* Botón de eliminar */}
                      <div className="ml-4">
                        <button
                          onClick={() => handleDeleteKey(key.id, key.name)}
                          disabled={keysLoading}
                          className={`p-2 rounded-lg transition ${
                            keysLoading
                              ? 'bg-gray-100 text-gray-400 cursor-not-allowed dark:bg-gray-800 dark:text-gray-600'
                              : 'bg-red-100 hover:bg-red-200 text-red-700 dark:bg-red-900 dark:hover:bg-red-800 dark:text-red-300'
                          }`}
                          title="Eliminar llave"
                        >
                          <Trash2 size={16} />
                        </button>
                      </div>
                    </div>
                  </div>
                    ))
                  )}
                </div>
              )}
            </>
          )}

          {/* Sección de Perfil */}
          {activeSection === 'profile' && (
            <>
              <div className="flex justify-between items-center mb-6">
                <h1 className="text-3xl font-bold">Mi Perfil</h1>
                <button
                  onClick={handleEditProfile}
                  className="flex items-center gap-2 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition"
                >
                  <Edit size={16} />
                  Editar Perfil
                </button>
              </div>

              {/* Información del perfil */}
              <div className={`p-6 rounded-lg border shadow-sm ${
                darkMode ? 'bg-gray-700 border-gray-600' : 'bg-white border-gray-200'
              }`}>
                {currentUser ? (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-medium mb-1">Nombre Completo</label>
                      <p className="text-lg">{currentUser.firstName} {currentUser.lastName}</p>
                    </div>
                    <div>
                      <label className="block text-sm font-medium mb-1">Email</label>
                      <p className="text-lg">{currentUser.email}</p>
                    </div>
                    <div>
                      <label className="block text-sm font-medium mb-1">Empresa</label>
                      <p className="text-lg">{currentUser.company || 'No especificada'}</p>
                    </div>
                    <div>
                      <label className="block text-sm font-medium mb-1">Fecha de Registro</label>
                      <p className="text-lg">{new Date(currentUser.createdAt).toLocaleDateString()}</p>
                    </div>
                  </div>
                ) : (
                  <div className="text-center py-4 text-gray-500">
                    Cargando información del perfil...
                  </div>
                )}
              </div>
            </>
          )}

          {/* Sección de Configuración */}
          {activeSection === 'settings' && (
            <div>
              <h1 className="text-3xl font-bold mb-4">Configuración</h1>
              <p className="text-gray-600 dark:text-gray-300">
                Ajustes de cuenta y preferencias del sistema.
              </p>

              <div className={`mt-6 p-6 rounded-lg border ${
                darkMode ? 'bg-gray-700 border-gray-600' : 'bg-white border-gray-200'
              }`}>
                <h3 className="text-lg font-semibold mb-4">Preferencias</h3>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span>Modo Oscuro</span>
                    <button
                      onClick={toggleDarkMode}
                      className={`px-4 py-2 rounded transition ${
                        darkMode
                          ? 'bg-yellow-600 hover:bg-yellow-700 text-white'
                          : 'bg-gray-600 hover:bg-gray-700 text-white'
                      }`}
                    >
                      {darkMode ? 'Activado' : 'Desactivado'}
                    </button>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </main>

      {/* Modal para Subir Nueva Llave */}
      {showGenerateKey && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className={`p-6 rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto ${
            darkMode ? 'bg-gray-800 text-white' : 'bg-white text-gray-900'
          }`}>
            <div className="flex justify-between items-center mb-6">
              <h3 className="text-xl font-bold flex items-center gap-2">
                <Upload size={24} />
                Subir Nueva Llave a CTM
              </h3>
              <button
                onClick={() => setShowGenerateKey(false)}
                className="p-1 hover:bg-gray-200 dark:hover:bg-gray-700 rounded"
              >
                <X size={20} />
              </button>
            </div>

            <form onSubmit={(e) => { e.preventDefault(); handleUploadKey(); }} className="space-y-4">
              {/* Nombre de la llave */}
              <div>
                <label className="block text-sm font-medium mb-2">
                  Nombre de la Llave *
                </label>
                <input
                  type="text"
                  value={keyData.key_name}
                  onChange={(e) => handleKeyDataChange('key_name', e.target.value)}
                  className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                    darkMode
                      ? 'bg-gray-700 border-gray-600 text-white'
                      : 'bg-white border-gray-300 text-gray-900'
                  }`}
                  placeholder="mi-llave-encriptacion-001"
                  required
                  disabled={keysLoading}
                />
                <p className="text-xs text-gray-500 mt-1">
                  Solo letras, números, guiones y guiones bajos
                </p>
              </div>

              {/* Algoritmo */}
              <div>
                <label className="block text-sm font-medium mb-2">
                  Algoritmo
                </label>
                <select
                  value={keyData.algorithm}
                  onChange={(e) => handleKeyDataChange('algorithm', e.target.value)}
                  className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                    darkMode
                      ? 'bg-gray-700 border-gray-600 text-white'
                      : 'bg-white border-gray-300 text-gray-900'
                  }`}
                  disabled={keysLoading}
                >
                  <option value="AES">AES</option>
                  <option value="RSA">RSA</option>
                  <option value="HMAC">HMAC</option>
                </select>
              </div>

              {/* Número de bytes */}
              <div>
                <label className="block text-sm font-medium mb-2">
                  Número de Bytes
                </label>
                <input
                  type="number"
                  value={keyData.num_bytes}
                  onChange={(e) => handleKeyDataChange('num_bytes', parseInt(e.target.value))}
                  className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                    darkMode
                      ? 'bg-gray-700 border-gray-600 text-white'
                      : 'bg-white border-gray-300 text-gray-900'
                  }`}
                  min="1"
                  max="1024"
                  disabled={keysLoading}
                />
                <p className="text-xs text-gray-500 mt-1">
                  Entre 1 y 1024 bytes
                </p>
              </div>

              {/* Exportable */}
              <div className="flex items-center gap-3">
                <input
                  type="checkbox"
                  id="exportable"
                  checked={keyData.exportable}
                  onChange={(e) => handleKeyDataChange('exportable', e.target.checked)}
                  className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                  disabled={keysLoading}
                />
                <label htmlFor="exportable" className="text-sm font-medium">
                  Llave exportable
                </label>
              </div>

              {/* Material de la llave */}
              <div>
                <label className="block text-sm font-medium mb-2">
                  Material de la Llave (Base64)
                </label>
                <div className="flex gap-2">
                  <textarea
                    value={keyData.key_material_base64}
                    onChange={(e) => handleKeyDataChange('key_material_base64', e.target.value)}
                    className={`flex-1 px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                      darkMode
                        ? 'bg-gray-700 border-gray-600 text-white'
                        : 'bg-white border-gray-300 text-gray-900'
                    }`}
                    rows="3"
                    placeholder="Opcional - se generará automáticamente si se deja vacío"
                    disabled={keysLoading}
                  />
                  <button
                    type="button"
                    onClick={generateRandomKey}
                    className={`px-4 py-2 rounded-lg transition ${
                      darkMode
                        ? 'bg-gray-600 hover:bg-gray-500 text-white'
                        : 'bg-gray-100 hover:bg-gray-200 text-gray-700'
                    }`}
                    disabled={keysLoading}
                  >
                    Generar
                  </button>
                </div>
                <p className="text-xs text-gray-500 mt-1">
                  Si se deja vacío, se generará automáticamente
                </p>
              </div>

              {/* Botones */}
              <div className="flex gap-3 pt-4">
                <button
                  type="submit"
                  disabled={keysLoading || !keyData.key_name}
                  className={`flex-1 py-3 px-4 rounded-lg font-medium transition flex items-center justify-center gap-2 ${
                    keysLoading || !keyData.key_name
                      ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                      : 'bg-blue-600 hover:bg-blue-700 text-white'
                  }`}
                >
                  {keysLoading ? (
                    <>
                      <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                      Subiendo...
                    </>
                  ) : (
                    <>
                      <Upload size={20} />
                      Subir a CTM
                    </>
                  )}
                </button>
                <button
                  type="button"
                  onClick={() => setShowGenerateKey(false)}
                  className={`flex-1 px-4 py-3 rounded-lg transition ${
                    darkMode
                      ? 'bg-gray-700 hover:bg-gray-600 text-white'
                      : 'bg-gray-200 hover:bg-gray-300 text-gray-900'
                  }`}
                  disabled={keysLoading}
                >
                  Cancelar
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Modal para Editar Perfil */}
      {showEditProfile && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className={`p-6 rounded-lg shadow-xl max-w-md w-full mx-4 ${
            darkMode ? 'bg-gray-800 text-white' : 'bg-white text-gray-900'
          }`}>
            <div className="flex justify-between items-center mb-6">
              <h3 className="text-xl font-bold">Editar Mi Perfil</h3>
              <button
                onClick={() => setShowEditProfile(false)}
                className="p-1 hover:bg-gray-200 dark:hover:bg-gray-700 rounded"
              >
                <X size={20} />
              </button>
            </div>

            {/* Mostrar errores */}
            {userError && (
              <div className="mb-4 p-4 bg-red-100 border border-red-400 text-red-700 rounded-lg">
                Error: {userError}
                <button
                  onClick={clearUserError}
                  className="ml-2 text-red-500 hover:text-red-700"
                >
                  ✕
                </button>
              </div>
            )}

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium mb-1">Nombre *</label>
                <input
                  type="text"
                  value={editingProfile.firstName}
                  onChange={(e) => handleProfileChange('firstName', e.target.value)}
                  className={`w-full px-3 py-2 border rounded-lg ${
                    darkMode
                      ? 'bg-gray-700 border-gray-600 text-white'
                      : 'bg-white border-gray-300 text-gray-900'
                  }`}
                />
              </div>

              <div>
                <label className="block text-sm font-medium mb-1">Apellido *</label>
                <input
                  type="text"
                  value={editingProfile.lastName}
                  onChange={(e) => handleProfileChange('lastName', e.target.value)}
                  className={`w-full px-3 py-2 border rounded-lg ${
                    darkMode
                      ? 'bg-gray-700 border-gray-600 text-white'
                      : 'bg-white border-gray-300 text-gray-900'
                  }`}
                />
              </div>

              <div>
                <label className="block text-sm font-medium mb-1">Email *</label>
                <input
                  type="email"
                  value={editingProfile.email}
                  onChange={(e) => handleProfileChange('email', e.target.value)}
                  className={`w-full px-3 py-2 border rounded-lg ${
                    darkMode
                      ? 'bg-gray-700 border-gray-600 text-white'
                      : 'bg-white border-gray-300 text-gray-900'
                  }`}
                />
              </div>

              <div>
                <label className="block text-sm font-medium mb-1">Empresa</label>
                <input
                  type="text"
                  value={editingProfile.company}
                  onChange={(e) => handleProfileChange('company', e.target.value)}
                  className={`w-full px-3 py-2 border rounded-lg ${
                    darkMode
                      ? 'bg-gray-700 border-gray-600 text-white'
                      : 'bg-white border-gray-300 text-gray-900'
                  }`}
                />
              </div>

              {/* Información de solo lectura sobre configuración */}
              <div className="mt-6 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                <h5 className="font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Configuración de Servicios
                </h5>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  La configuración de CipherTrust Manager y SEQRNG es gestionada por el administrador del sistema.
                  Si necesitas modificar estos ajustes, contacta a tu administrador.
                </p>
              </div>
            </div>

            <div className="flex gap-3 mt-6">
              <button
                onClick={updateProfile}
                disabled={isSavingProfile}
                className={`flex-1 px-4 py-2 rounded-lg transition flex items-center justify-center gap-2 ${
                  isSavingProfile
                    ? 'bg-gray-400 cursor-not-allowed'
                    : 'bg-blue-600 hover:bg-blue-700'
                } text-white`}
              >
                {isSavingProfile ? (
                  <>
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                    Guardando...
                  </>
                ) : (
                  <>
                    <Save size={16} />
                    Guardar Cambios
                  </>
                )}
              </button>
              <button
                onClick={() => setShowEditProfile(false)}
                disabled={isSavingProfile}
                className={`flex-1 px-4 py-2 rounded-lg transition ${
                  isSavingProfile
                    ? 'opacity-50 cursor-not-allowed'
                    : darkMode
                      ? 'bg-gray-700 hover:bg-gray-600 text-white'
                      : 'bg-gray-200 hover:bg-gray-300 text-gray-900'
                }`}
              >
                Cancelar
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default UsuarioDashboard;
