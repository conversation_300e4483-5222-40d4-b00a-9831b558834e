import { Trash2, AlertTriangle } from 'lucide-react';
import PropTypes from 'prop-types';
import { Modal } from '../../common';

/**
 * Modal elegante para confirmar eliminación de llave
 */
const KeyDeleteModal = ({ isOpen, onClose, onConfirm, keyName, darkMode }) => {
  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={
        <div className="flex items-center gap-4">
          <div className="p-3 rounded-xl bg-gradient-to-br from-red-500 to-rose-600 shadow-lg">
            <AlertTriangle size={24} className="text-white" />
          </div>
          <div>
            <h3 className="text-2xl font-light tracking-wide leading-relaxed text-gray-900 dark:text-white">
              Confirmar Eliminación
            </h3>
            <p className="text-sm font-light tracking-wide text-gray-600 dark:text-gray-400">
              Esta acción es irreversible
            </p>
          </div>
        </div>
      }
      maxWidth="max-w-lg"
      darkMode={darkMode}
    >
      {/* Contenido del modal */}
      <div className="space-y-6">
        {/* Mensaje de advertencia */}
        <div className={`p-6 rounded-2xl border-2 shadow-lg ${
          darkMode 
            ? 'bg-gradient-to-br from-red-900/20 to-rose-900/20 border-red-700' 
            : 'bg-gradient-to-br from-red-50 to-rose-100 border-red-200'
        }`}>
          <div className="flex items-start gap-4">
            <div className="p-3 rounded-xl bg-gradient-to-br from-red-500 to-rose-600 shadow-md flex-shrink-0">
              <Trash2 size={24} className="text-white" />
            </div>
            <div className="flex-1">
              <h4 className="text-lg font-light tracking-wide text-red-700 dark:text-red-300 mb-3">
                ¿Estás seguro de que deseas eliminar la llave?
              </h4>
              <div className={`p-4 rounded-xl border-2 mb-4 ${
                darkMode 
                  ? 'bg-gray-900/50 border-gray-600' 
                  : 'bg-white border-gray-200'
              }`}>
                <p className="font-mono text-lg font-medium text-gray-900 dark:text-white">
                  "{keyName}"
                </p>
              </div>
              <p className="text-sm font-light tracking-wide text-red-600 dark:text-red-400 leading-relaxed">
                Esta acción no se puede deshacer. La llave será eliminada permanentemente del sistema.
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Footer con botones centrados */}
      <div className="flex justify-center gap-4 mt-8 pt-6 border-t-2 border-gray-200 dark:border-gray-600 bg-gray-50/30 dark:bg-gray-700/20 -mx-8 px-8 pb-6 rounded-b-2xl">
        <button
          onClick={onConfirm}
          className="px-8 py-3 rounded-xl font-light tracking-wide shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 bg-gradient-to-r from-red-600 to-red-700 hover:from-red-700 hover:to-red-800 text-white flex items-center gap-3"
        >
          <div className="p-1 bg-white/20 rounded-lg">
            <Trash2 size={16} />
          </div>
          <span>
            Eliminar Llave
          </span>
        </button>
        
        <button
          onClick={onClose}
          className="px-8 py-3 rounded-xl font-light tracking-wide border-2 border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 hover:border-gray-400 dark:hover:border-gray-500 transform hover:scale-105 transition-all duration-300 shadow-lg hover:shadow-xl"
        >
          Cancelar
        </button>
      </div>
    </Modal>
  );
};

KeyDeleteModal.propTypes = {
  isOpen: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  onConfirm: PropTypes.func.isRequired,
  keyName: PropTypes.string.isRequired,
  darkMode: PropTypes.bool.isRequired
};

export default KeyDeleteModal;
