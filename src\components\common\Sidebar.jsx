import { useRef, useEffect, useState } from 'react';
import { Sun, Moon, User, Mail } from 'lucide-react';
import gsap from 'gsap';
import PropTypes from 'prop-types';
import Button from './Button';
import LogoutConfirmModal from './LogoutConfirmModal';
import logoUsuario from '../../assets/logo-usuario.png';
import logoAdmin from '../../assets/Logo-admin1.png';

/**
 * Componente Sidebar reutilizable
 * Proporciona navegación lateral consistente para ambos dashboards
 */
const Sidebar = ({ 
  title,
  currentUser,
  darkMode,
  onToggleDarkMode,
  onLogout,
  navigationItems,
  activeSection,
  onSectionChange
}) => {
  const sidebarRef = useRef(null);
  const sunIconRef = useRef(null);
  const moonIconRef = useRef(null);
  const [showLogoutModal, setShowLogoutModal] = useState(false);

  // Funciones para manejar logout
  const handleLogoutClick = () => {
    setShowLogoutModal(true);
  };

  const handleLogoutConfirm = () => {
    setShowLogoutModal(false);
    onLogout();
  };

  const handleLogoutCancel = () => {
    setShowLogoutModal(false);
  };

  // Animaciones GSAP al montar (solo escritorio)
  useEffect(() => {
    if (window.innerWidth >= 768) {
      gsap.fromTo(
        sidebarRef.current,
        { x: -100, opacity: 0 },
        { x: 0, opacity: 1, duration: 1, ease: "power2.out" }
      );
    }
  }, []);

  // Animaciones de iconos según el modo
  useEffect(() => {
    if (darkMode) {
      // Modo oscuro: Sol rotando
      if (sunIconRef.current) {
        gsap.to(sunIconRef.current, {
          rotation: 360,
          duration: 4,
          ease: "none",
          repeat: -1
        });
      }
    } else {
      // Modo claro: Luna respirando
      if (moonIconRef.current) {
        gsap.to(moonIconRef.current, {
          scale: 1.2,
          opacity: 0.7,
          duration: 1.5,
          ease: "power2.inOut",
          repeat: -1,
          yoyo: true
        });
      }
    }

    // Limpiar animaciones al cambiar modo
    return () => {
      if (sunIconRef.current) gsap.killTweensOf(sunIconRef.current);
      if (moonIconRef.current) gsap.killTweensOf(moonIconRef.current);
    };
  }, [darkMode]);

  return (
    <>
      <aside
        ref={sidebarRef}
        className={`w-64 sm:w-72 h-full shadow-xl z-10 p-6 hidden lg:flex flex-col justify-between transition-colors duration-500
        ${darkMode ? "bg-gray-800 text-white" : "bg-white text-gray-800"}`}
      >
      {/* Información del Usuario */}
      <div>
        {/* Logo combinado para admin, texto para usuario */}
        {title === "Quantum Admin" ? (
          <div className="mb-4 text-center">
            <div className="flex items-center justify-center gap-3">
              {/* Icono de admin más grande */}
              <img
                src="/src/assets/Logo-admin1.png"
                alt="Admin Icon"
                className="w-12 h-12 object-contain"
              />
              {/* Logo SECURE horizontal más grande */}
              <img
                src="/src/assets/sequre-logo-negro.svg"
                alt="SECURE Logo"
                className={`w-28 h-8 object-contain ${darkMode ? 'filter invert' : ''}`}
              />
            </div>
          </div>
        ) : (
          <div className="mb-6 text-center">
            <div className="flex items-center justify-center gap-3">
              {/* Icono del usuario al lado del logo SECURE */}
              {currentUser && (
                <div className="w-12 h-12 rounded-full overflow-hidden shadow-lg border-2 border-white dark:border-gray-600">
                  <img
                    src={logoUsuario}
                    alt="Logo Usuario"
                    className="w-full h-full object-cover"
                  />
                </div>
              )}
              {/* Logo SECURE horizontal */}
              <img
                src="/src/assets/sequre-logo-negro.svg"
                alt="SECURE Logo"
                className={`w-28 h-8 object-contain ${darkMode ? 'filter invert' : ''}`}
              />
            </div>
          </div>
        )}

        {/* Información del usuario */}
        {currentUser && (
          <div className={`mb-8 p-4 rounded-xl border ${darkMode ? 'bg-gray-700 border-gray-600' : 'bg-gray-50 border-gray-200'} shadow-lg`}>
            <div className="flex items-center gap-3 mb-2">
              <User size={16} className="text-blue-500" />
              <span className="font-light tracking-wide text-sm">
                {currentUser.role === 'ADMIN' ? 'Super Admin' : `${currentUser.firstName} ${currentUser.lastName}`}
              </span>
            </div>
            <div className="flex items-center gap-3">
              <Mail size={16} className="text-green-500" />
              <span className={`text-xs font-light tracking-wide ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                {currentUser.email}
              </span>
            </div>
          </div>
        )}

        {/* Navegación */}
        <nav className="space-y-3">
          {navigationItems.map((item) => (
            <button
              key={item.key}
              onClick={() => onSectionChange(item.key)}
              className={`flex items-center gap-3 px-4 py-3 rounded-xl transition-all duration-300 w-full text-left font-light tracking-wide ${
                activeSection === item.key
                  ? (darkMode ? 'bg-blue-900 text-blue-300 shadow-lg' : 'bg-blue-100 text-blue-700 shadow-lg')
                  : (darkMode ? 'hover:bg-gray-700 hover:shadow-md' : 'hover:bg-gray-100 hover:shadow-md')
              }`}
            >
              <item.icon size={18} /> <span>{item.label}</span>
            </button>
          ))}
        </nav>
      </div>

      {/* Botones elegantes como en el proyecto anterior */}
      <div className="flex flex-col gap-3">
        <button
          onClick={onToggleDarkMode}
          className={`w-full flex items-center justify-center gap-3 py-3 px-4 rounded-xl font-light tracking-wide shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 group ${
            darkMode
              ? "bg-gradient-to-r from-yellow-400 to-yellow-600 hover:from-yellow-500 hover:to-yellow-700 text-gray-900"
              : "bg-gradient-to-r from-gray-500 to-gray-600 hover:from-gray-600 hover:to-gray-700 text-white"
          }`}
        >
          <div className={`p-1 rounded-lg group-hover:bg-white/30 transition-all duration-300 ${
            darkMode ? "bg-gray-900/20" : "bg-white/20"
          }`}>
            {darkMode ? (
              <Sun ref={sunIconRef} size={16} className="text-gray-900" />
            ) : (
              <Moon ref={moonIconRef} size={16} className="text-white" />
            )}
          </div>
          <span className="font-light tracking-wide">
            {darkMode ? 'Modo Claro' : 'Modo Oscuro'}
          </span>
        </button>

        <button
          onClick={handleLogoutClick}
          className="w-full flex items-center justify-center gap-3 py-3 px-4 rounded-xl font-light tracking-wide shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 group bg-gradient-to-r from-red-600 to-red-700 hover:from-red-700 hover:to-red-800 text-white"
        >
          <div className="p-1 bg-white/20 rounded-lg group-hover:bg-white/30 transition-all duration-300">
            <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M3 3a1 1 0 00-1 1v12a1 1 0 102 0V4a1 1 0 00-1-1zm10.293 9.293a1 1 0 001.414 1.414l3-3a1 1 0 000-1.414l-3-3a1 1 0 10-1.414 1.414L14.586 9H7a1 1 0 100 2h7.586l-1.293 1.293z" clipRule="evenodd" />
            </svg>
          </div>
          <span className="font-light tracking-wide">
            Cerrar Sesión
          </span>
        </button>
      </div>
      </aside>

      {/* Modal de confirmación de logout */}
      <LogoutConfirmModal
        isOpen={showLogoutModal}
        onConfirm={handleLogoutConfirm}
        onCancel={handleLogoutCancel}
        darkMode={darkMode}
      />
    </>
  );
};

Sidebar.propTypes = {
  title: PropTypes.string.isRequired,
  currentUser: PropTypes.object,
  darkMode: PropTypes.bool.isRequired,
  onToggleDarkMode: PropTypes.func.isRequired,
  onLogout: PropTypes.func.isRequired,
  navigationItems: PropTypes.arrayOf(PropTypes.shape({
    key: PropTypes.string.isRequired,
    label: PropTypes.string.isRequired,
    icon: PropTypes.elementType.isRequired
  })).isRequired,
  activeSection: PropTypes.string.isRequired,
  onSectionChange: PropTypes.func.isRequired
};

export default Sidebar;
