import { useState, useEffect } from 'react';
import { Save, User, Mail, X } from 'lucide-react';
import PropTypes from 'prop-types';
import { useLanguage } from '../../../i18n/LanguageContext';
import { Modal, Button, FormField, ErrorAlert } from '../../common';

/**
 * Modal para editar perfil del usuario
 */
const ProfileEditModal = ({ isOpen, currentUser, onClose, onUpdate, darkMode }) => {
  const { t } = useLanguage();
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    company: ''
  });
  const [isSaving, setIsSaving] = useState(false);
  const [error, setError] = useState(null);

  useEffect(() => {
    if (currentUser) {
      setFormData({
        firstName: currentUser.firstName || '',
        lastName: currentUser.lastName || '',
        email: currentUser.email || '',
        company: currentUser.company || ''
      });
    }
  }, [currentUser]);

  const handleChange = (field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSaving(true);
    setError(null);
    
    try {
      // Solo enviar los campos que el usuario puede modificar
      const allowedFields = {
        firstName: formData.firstName,
        lastName: formData.lastName,
        email: formData.email,
        company: formData.company
      };
      
      await onUpdate(allowedFields);
    } catch (err) {
      setError(err.message || 'Error al actualizar el perfil');
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={
        <div className="flex items-center gap-3">
          <div className="p-2 rounded-xl bg-gradient-to-r from-blue-500 to-blue-600 shadow-lg">
            <User size={20} className="text-white" />
          </div>
          <span className="text-xl font-light tracking-wide">{t('modals.editProfile')}</span>
        </div>
      }
      maxWidth="max-w-lg"
      darkMode={darkMode}
    >
      <form onSubmit={handleSubmit} className="space-y-6">
        <ErrorAlert error={error} onClose={() => setError(null)} />

        {/* Información Personal */}
        <div className={`p-6 rounded-xl border-2 ${
          darkMode
            ? 'bg-gray-700/50 border-gray-600/50'
            : 'bg-gray-50/50 border-gray-200/50'
        }`}>
          <div className="flex items-center gap-2 mb-4">
            <User size={18} className="text-blue-500" />
            <h4 className="text-lg font-medium text-gray-900 dark:text-white">
              {t('modals.personalInformation')}
            </h4>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            <div className="group">
              <FormField
                label={t('profile.firstName')}
                type="text"
                value={formData.firstName}
                onChange={(e) => handleChange('firstName', e.target.value)}
                required
                darkMode={darkMode}
                className="transition-all duration-300 group-hover:shadow-md"
              />
            </div>

            <div className="group">
              <FormField
                label={t('profile.lastName')}
                type="text"
                value={formData.lastName}
                onChange={(e) => handleChange('lastName', e.target.value)}
                required
                darkMode={darkMode}
                className="transition-all duration-300 group-hover:shadow-md"
              />
            </div>
          </div>
        </div>

        {/* Información de Contacto */}
        <div className={`p-6 rounded-xl border-2 ${
          darkMode
            ? 'bg-gray-700/50 border-gray-600/50'
            : 'bg-gray-50/50 border-gray-200/50'
        }`}>
          <div className="flex items-center gap-2 mb-4">
            <Mail size={18} className="text-green-500" />
            <h4 className="text-lg font-medium text-gray-900 dark:text-white">
              {t('modals.contactInformation')}
            </h4>
          </div>

          <div className="space-y-4">
            <div className="group">
              <FormField
                label={t('profile.email')}
                type="email"
                value={formData.email}
                onChange={(e) => handleChange('email', e.target.value)}
                required
                darkMode={darkMode}
                className="transition-all duration-300 group-hover:shadow-md"
              />
            </div>

            <div className="group">
              <FormField
                label={t('profile.company')}
                type="text"
                value={formData.company}
                onChange={(e) => handleChange('company', e.target.value)}
                darkMode={darkMode}
                placeholder="Nombre de tu empresa (opcional)"
                className="transition-all duration-300 group-hover:shadow-md"
              />
            </div>
          </div>
        </div>

        {/* Información de Configuración */}
        <div className={`p-6 rounded-xl border-2 ${
          darkMode
            ? 'bg-amber-900/20 border-amber-700/50'
            : 'bg-amber-50/50 border-amber-200/50'
        }`}>
          <div className="flex items-center gap-2 mb-3">
            <div className="p-1 rounded-lg bg-amber-500/20">
              <Save size={16} className="text-amber-600" />
            </div>
            <h5 className="font-medium text-amber-800 dark:text-amber-300">
              Configuración de Servicios
            </h5>
          </div>
          <p className="text-sm text-amber-700 dark:text-amber-400 leading-relaxed">
            La configuración de CipherTrust Manager y SEQRNG es gestionada por el administrador del sistema.
            Si necesitas modificar estos ajustes, contacta a tu administrador.
          </p>
        </div>

        {/* Botones centrados */}
        <div className="flex justify-center gap-3 pt-4">
          <button
            type="submit"
            disabled={isSaving}
            className="group relative overflow-hidden px-4 py-2 rounded-xl font-light tracking-wide text-white bg-gradient-to-r from-blue-500 via-blue-600 to-blue-700 hover:from-blue-600 hover:via-blue-700 hover:to-blue-800 shadow-lg hover:shadow-xl hover:shadow-blue-500/30 transform hover:scale-105 transition-all duration-300 border border-blue-400/30 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
          >
            <div className="absolute inset-0 bg-gradient-to-r from-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
            <div className="relative flex items-center gap-2">
              {isSaving ? (
                <>
                  <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                  <span>{t('profile.updating')}</span>
                </>
              ) : (
                <>
                  <div className="p-1 rounded-lg bg-white/20 group-hover:bg-white/30 transition-all duration-300">
                    <Save size={14} />
                  </div>
                  <span>{t('profile.updateProfile')}</span>
                </>
              )}
            </div>
          </button>

          <button
            type="button"
            onClick={onClose}
            disabled={isSaving}
            className="group relative overflow-hidden px-4 py-2 rounded-xl font-light tracking-wide bg-gray-600 hover:bg-gray-700 text-white shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 border border-gray-500/30 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
          >
            <div className="absolute inset-0 bg-gradient-to-r from-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
            <div className="relative flex items-center gap-2">
              <div className="p-1 rounded-lg bg-white/20 group-hover:bg-white/30 transition-all duration-300">
                <X size={14} />
              </div>
              <span>{t('modals.cancel')}</span>
            </div>
          </button>
        </div>
      </form>
    </Modal>
  );
};

ProfileEditModal.propTypes = {
  isOpen: PropTypes.bool.isRequired,
  currentUser: PropTypes.object,
  onClose: PropTypes.func.isRequired,
  onUpdate: PropTypes.func.isRequired,
  darkMode: PropTypes.bool.isRequired
};

export default ProfileEditModal;
