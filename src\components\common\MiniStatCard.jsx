import PropTypes from 'prop-types';

/**
 * Componente reutilizable para mostrar tarjetas de estadísticas pequeñas
 * Usado para mostrar métricas secundarias en formato compacto
 */
const MiniStatCard = ({ 
  value, 
  label, 
  valueColor = "text-gray-900 dark:text-white",
  isLoading = false,
  className = ""
}) => {
  return (
    <div className={`p-4 rounded-xl border bg-white dark:bg-gray-700 dark:border-gray-600 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 ${className}`}>
      <div className="text-center">
        <p className={`text-xl font-light tracking-wide leading-relaxed ${valueColor}`}>
          {isLoading ? '...' : value}
        </p>
        <p className="text-sm font-light tracking-wide text-gray-600 dark:text-gray-300">{label}</p>
      </div>
    </div>
  );
};

MiniStatCard.propTypes = {
  value: PropTypes.oneOfType([PropTypes.string, PropTypes.number]).isRequired,
  label: PropTypes.string.isRequired,
  valueColor: PropTypes.string,
  isLoading: PropTypes.bool,
  className: PropTypes.string
};

export default MiniStatCard;
