import PropTypes from 'prop-types';

/**
 * Componente LoadingSpinner reutilizable
 * Muestra un indicador de carga consistente en toda la aplicación
 */
const LoadingSpinner = ({ 
  size = "md", 
  message = "Cargando...",
  className = "",
  showMessage = true
}) => {
  const sizes = {
    sm: "w-4 h-4",
    md: "w-8 h-8",
    lg: "w-12 h-12"
  };

  return (
    <div className={`flex items-center justify-center py-8 ${className}`}>
      <div className={`${sizes[size]} border-4 border-blue-500 border-t-transparent rounded-full animate-spin`}></div>
      {showMessage && <span className="ml-3">{message}</span>}
    </div>
  );
};

LoadingSpinner.propTypes = {
  size: PropTypes.oneOf(['sm', 'md', 'lg']),
  message: PropTypes.string,
  className: PropTypes.string,
  showMessage: PropTypes.bool
};

export default LoadingSpinner;
