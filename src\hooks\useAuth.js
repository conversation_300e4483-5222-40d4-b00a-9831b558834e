/**
 * useAuth Hook
 * Hook personalizado para manejo de autenticación
 */

import { useState, useEffect, useCallback } from 'react';
import { authService } from '../services/index.js';

export const useAuth = () => {
  const [user, setUser] = useState(null);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);

  /**
   * Verificar estado de autenticación al cargar
   */
  useEffect(() => {
    const checkAuthStatus = async () => {
      try {
        setIsLoading(true);

        if (authService.isTokenValid()) {
          const profile = await authService.getProfile();
          setUser(profile);
          setIsAuthenticated(true);
        } else {
          setUser(null);
          setIsAuthenticated(false);
        }
      } catch (error) {
        setUser(null);
        setIsAuthenticated(false);
        setError(error.message);
      } finally {
        setIsLoading(false);
      }
    };

    checkAuthStatus();
  }, []);

  /**
   * Iniciar sesión
   */
  const login = useCallback(async (email, password) => {
    try {
      setIsLoading(true);
      setError(null);
      
      const response = await authService.login(email, password);
      setUser(response.user);
      setIsAuthenticated(true);
      
      return response;
    } catch (error) {
      setError(error.message);
      setUser(null);
      setIsAuthenticated(false);
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, []);

  /**
   * Cerrar sesión
   */
  const logout = useCallback(() => {
    authService.logout();
    setUser(null);
    setIsAuthenticated(false);
    setError(null);
  }, []);

  /**
   * Renovar token
   */
  const refreshToken = useCallback(async () => {
    try {
      const refreshToken = authService.getRefreshToken();
      if (!refreshToken) {
        throw new Error('No refresh token available');
      }
      
      const response = await authService.refreshToken(refreshToken);
      setUser(response.user);
      setIsAuthenticated(true);
      
      return response;
    } catch (error) {
      setError(error.message);
      logout();
      throw error;
    }
  }, [logout]);

  /**
   * Obtener perfil actualizado
   */
  const getProfile = useCallback(async () => {
    try {
      const profile = await authService.getProfile();
      setUser(profile);
      return profile;
    } catch (error) {
      setError(error.message);
      throw error;
    }
  }, []);

  /**
   * Verificar si el usuario tiene un rol específico
   */
  const hasRole = useCallback((role) => {
    if (!user) return false;
    return user.role === role || user.role === 'ADMIN';
  }, [user]);

  /**
   * Verificar si el usuario es admin
   */
  const isAdmin = useCallback(() => {
    return hasRole('ADMIN');
  }, [hasRole]);

  /**
   * Limpiar errores
   */
  const clearError = useCallback(() => {
    setError(null);
  }, []);

  return {
    // Estado
    user,
    isAuthenticated,
    isLoading,
    error,
    
    // Acciones
    login,
    logout,
    refreshToken,
    getProfile,
    clearError,
    
    // Utilidades
    hasRole,
    isAdmin,
  };
};
