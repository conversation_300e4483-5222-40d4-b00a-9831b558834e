import { useState } from 'react';
import { Edit, Lock, User, Mail, Building, Calendar } from 'lucide-react';
import PropTypes from 'prop-types';
import { useLanguage } from '../../../i18n/LanguageContext';
import { But<PERSON>, ErrorAlert } from '../../common';
import ProfileEditModal from './ProfileEditModal';
import PasswordChangeModal from './PasswordChangeModal';

/**
 * Componente para gestión del perfil del usuario
 */
const ProfileManagement = ({
  currentUser,
  isLoading,
  error,
  onClearError,
  onUpdateProfile,
  onChangePassword,
  darkMode
}) => {
  const { t } = useLanguage();
  const [showEditModal, setShowEditModal] = useState(false);
  const [showPasswordModal, setShowPasswordModal] = useState(false);

  const handleEditProfile = () => {
    setShowEditModal(true);
  };

  const handleUpdateProfile = async (profileData) => {
    await onUpdateProfile(profileData);
    setShowEditModal(false);
  };

  const handleChangePassword = async (passwordData) => {
    await onChangePassword(passwordData);
    setShowPasswordModal(false);
  };

  return (
    <>
      {/* Header con título y botones */}
      <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4 mb-8">
        <div>
          <h1 className="text-3xl font-light tracking-wide leading-relaxed text-gray-900 dark:text-white">
            {t('profile.title')}
          </h1>
          <p className="text-gray-600 dark:text-gray-300 font-light tracking-wide">
            {t('profile.subtitle')}
          </p>
        </div>
        <div className="flex gap-3">
          <button
            onClick={() => setShowPasswordModal(true)}
            className="group relative overflow-hidden px-4 py-2 rounded-xl font-light tracking-wide text-white bg-gradient-to-r from-gray-500 via-gray-600 to-gray-700 hover:from-gray-600 hover:via-gray-700 hover:to-gray-800 shadow-lg hover:shadow-xl hover:shadow-gray-500/30 transform hover:scale-105 transition-all duration-300 border border-gray-400/30"
          >
            <div className="absolute inset-0 bg-gradient-to-r from-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
            <div className="relative flex items-center gap-2">
              <div className="p-1 rounded-lg bg-white/20 group-hover:bg-white/30 transition-all duration-300">
                <Lock size={14} />
              </div>
              <span>{t('profile.changePassword')}</span>
            </div>
          </button>

          <button
            onClick={handleEditProfile}
            className="group relative overflow-hidden px-4 py-2 rounded-xl font-light tracking-wide text-white bg-gradient-to-r from-blue-500 via-blue-600 to-blue-700 hover:from-blue-600 hover:via-blue-700 hover:to-blue-800 shadow-lg hover:shadow-xl hover:shadow-blue-500/30 transform hover:scale-105 transition-all duration-300 border border-blue-400/30"
          >
            <div className="absolute inset-0 bg-gradient-to-r from-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
            <div className="relative flex items-center gap-2">
              <div className="p-1 rounded-lg bg-white/20 group-hover:bg-white/30 transition-all duration-300">
                <Edit size={14} />
              </div>
              <span>{t('profile.editProfile')}</span>
            </div>
          </button>
        </div>
      </div>

      <ErrorAlert error={error} onClose={onClearError} />

      {/* Card principal del perfil */}
      <div className={`p-8 rounded-2xl border-2 shadow-xl ${
        darkMode
          ? 'bg-gray-800 border-gray-600/50 text-white'
          : 'bg-white border-gray-200/50 text-gray-900'
      }`}
      style={{
        backdropFilter: 'blur(10px)',
        background: darkMode
          ? 'rgba(31, 41, 55, 0.95)'
          : 'rgba(255, 255, 255, 0.95)'
      }}>
        {currentUser ? (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Nombre Completo */}
            <div className="group">
              <div className="flex items-center gap-3 mb-3">
                <div className="p-2 rounded-lg bg-blue-500/10">
                  <User size={20} className="text-blue-500" />
                </div>
                <label className="text-sm font-light tracking-wide text-gray-600 dark:text-gray-300">
                  {t('profile.fullName')}
                </label>
              </div>
              <p className="text-xl font-light tracking-wide text-gray-900 dark:text-white ml-11">
                {currentUser.firstName} {currentUser.lastName}
              </p>
            </div>

            {/* Email */}
            <div className="group">
              <div className="flex items-center gap-3 mb-3">
                <div className="p-2 rounded-lg bg-green-500/10">
                  <Mail size={20} className="text-green-500" />
                </div>
                <label className="text-sm font-light tracking-wide text-gray-600 dark:text-gray-300">
                  {t('profile.email')}
                </label>
              </div>
              <p className="text-xl font-light tracking-wide text-gray-900 dark:text-white ml-11 break-all">
                {currentUser.email}
              </p>
            </div>

            {/* Empresa */}
            <div className="group">
              <div className="flex items-center gap-3 mb-3">
                <div className="p-2 rounded-lg bg-purple-500/10">
                  <Building size={20} className="text-purple-500" />
                </div>
                <label className="text-sm font-light tracking-wide text-gray-600 dark:text-gray-300">
                  {t('profile.company')}
                </label>
              </div>
              <p className="text-xl font-light tracking-wide text-gray-900 dark:text-white ml-11">
                {currentUser.company || t('profile.notSpecified')}
              </p>
            </div>

            {/* Fecha de Registro */}
            <div className="group">
              <div className="flex items-center gap-3 mb-3">
                <div className="p-2 rounded-lg bg-orange-500/10">
                  <Calendar size={20} className="text-orange-500" />
                </div>
                <label className="text-sm font-light tracking-wide text-gray-600 dark:text-gray-300">
                  {t('profile.registrationDate')}
                </label>
              </div>
              <p className="text-xl font-light tracking-wide text-gray-900 dark:text-white ml-11">
                {currentUser.createdAt
                  ? new Date(currentUser.createdAt).toLocaleDateString('es-ES', {
                      day: '2-digit',
                      month: '2-digit',
                      year: 'numeric'
                    })
                  : 'N/A'
                }
              </p>
            </div>
          </div>
        ) : (
          <div className="text-center py-12">
            <div className="animate-pulse">
              <div className="w-16 h-16 bg-gray-300 dark:bg-gray-600 rounded-full mx-auto mb-4"></div>
              <div className="h-4 bg-gray-300 dark:bg-gray-600 rounded w-48 mx-auto mb-2"></div>
              <div className="h-3 bg-gray-300 dark:bg-gray-600 rounded w-32 mx-auto"></div>
            </div>
          </div>
        )}
      </div>

      <ProfileEditModal
        isOpen={showEditModal}
        currentUser={currentUser}
        onClose={() => setShowEditModal(false)}
        onUpdate={handleUpdateProfile}
        darkMode={darkMode}
      />

      <PasswordChangeModal
        isOpen={showPasswordModal}
        onClose={() => setShowPasswordModal(false)}
        onChangePassword={handleChangePassword}
        darkMode={darkMode}
      />
    </>
  );
};

ProfileManagement.propTypes = {
  currentUser: PropTypes.object,
  isLoading: PropTypes.bool.isRequired,
  error: PropTypes.string,
  onClearError: PropTypes.func.isRequired,
  onUpdateProfile: PropTypes.func.isRequired,
  onChangePassword: PropTypes.func.isRequired,
  darkMode: PropTypes.bool.isRequired
};

export default ProfileManagement;
